package com.senox.realty.schedule;

import com.senox.common.utils.DateUtils;
import com.senox.realty.service.AdvertisingStatisticsService;
import com.senox.realty.service.RealtyStatisticsService;
import com.senox.realty.vo.StatisticsGenerateVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/4/24 13:39
 */
@Component
@RequiredArgsConstructor
public class StatisticsSchedule {

    private final RealtyStatisticsService realtyStatisticsService;
    private final AdvertisingStatisticsService advertisingStatisticsService;

    /**
     * 生成物业统计报表
     */
    @XxlJob("generateRealtyStatisticsJob")
    public void generateRealtyStatisticsJob() {
        XxlJobHelper.log("物业统计生成开始...");
        long execStartTime = System.currentTimeMillis();

        LocalDate date = prepareDate(XxlJobHelper.getJobParam());
        XxlJobHelper.log("物业统计生成的日期 {} ...", date);

        StatisticsGenerateVo generateVo = new StatisticsGenerateVo();
        generateVo.setYear(date.getYear());
        generateVo.setMonth(date.getMonthValue());
        generateVo.setDay(date.getDayOfMonth());
        realtyStatisticsService.generateRealtyStatistics(generateVo);

        XxlJobHelper.log("物业统计生成结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    /**
     * 生成广告位统计报表
     */
    @XxlJob("generateAdvertisingStatisticsJob")
    public void generateAdvertisingStatisticsJob() {
        XxlJobHelper.log("广告位统计生成开始。。。");
        long execStartTime = System.currentTimeMillis();

        LocalDate date = prepareDate(XxlJobHelper.getJobParam());
        XxlJobHelper.log("广告位统计生成的日期 {} ...", date);

        StatisticsGenerateVo generateVo = new StatisticsGenerateVo();
        generateVo.setYear(date.getYear());
        generateVo.setMonth(date.getMonthValue());
        generateVo.setDay(date.getDayOfMonth());
        advertisingStatisticsService.generateAdvertisingStatistics(generateVo);

        XxlJobHelper.log("广告位统计生成结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    private LocalDate prepareDate(String dateStr) {
        LocalDate result = DateUtils.parseDate(dateStr, DateUtils.PATTERN_FULL_DATE);
        result = result == null ? LocalDate.now() : result;
        return result;
    }
}

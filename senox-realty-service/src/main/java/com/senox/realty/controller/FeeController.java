package com.senox.realty.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.realty.constant.FeeCategory;
import com.senox.realty.domain.Fee;
import com.senox.realty.service.FeeService;
import com.senox.realty.vo.FeeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.security.InvalidParameterException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/23 16:49
 */
@Api(tags = "费项")
@RestController
@RequestMapping("/fee")
public class FeeController extends BaseController {

    @Autowired
    private FeeService feeService;

    @ApiOperation("添加费项")
    @PostMapping("/add")
    public Long addFee(@Validated({Add.class}) @RequestBody FeeVo feeVo) {
        Fee fee = feeVo2Entity(feeVo);
        initEntityCreator(fee);
        initEntityModifier(fee);
        return feeService.addFee(fee);
    }

    @ApiOperation("修改费项")
    @PostMapping("/update")
    public void updateFee(@Validated({Update.class}) @RequestBody FeeVo feeVo) {
        if (feeVo.getId() < 1L) {
            throw new InvalidParameterException("无效id");
        }
        Fee fee = feeVo2Entity(feeVo);
        initEntityModifier(fee);
        feeService.updateFee(fee);
    }

    @ApiOperation("获取费项信息")
    @GetMapping("/get/{id}")
    public FeeVo getFee(@PathVariable Long id) {
        if (id < 1L) {
            throw new com.senox.common.exception.InvalidParameterException("无效的id");
        }
        Fee fee = feeService.findById(id);
        return fee == null ? null : fee2Vo(fee);
    }

    @ApiOperation("费项列表")
    @PostMapping("/list")
    public List<FeeVo> list(@RequestParam(required = false) Integer category) {
        List<Fee> list = feeService.listAll();
        // 费项类别
        if (!CollectionUtils.isEmpty(list) && category != null) {
            FeeCategory feeCategory = FeeCategory.fromValue(category);
            if (feeCategory == null) {
                list = Collections.emptyList();
            } else {
                list = list.stream().filter(x -> Objects.equals(x.getCategory(), feeCategory.getValue())).collect(Collectors.toList());
            }
        }

        return CollectionUtils.isEmpty(list) ? Collections.emptyList()
                : list.stream().map(this::fee2Vo).collect(Collectors.toList());
    }

    private Fee feeVo2Entity(FeeVo feeVo) {
        Fee result = new Fee();
        BeanUtils.copyProperties(feeVo, result);
        return result;
    }

    private FeeVo fee2Vo(Fee fee) {
        FeeVo result = new FeeVo();
        BeanUtils.copyProperties(fee, result);
        return result;
    }

}

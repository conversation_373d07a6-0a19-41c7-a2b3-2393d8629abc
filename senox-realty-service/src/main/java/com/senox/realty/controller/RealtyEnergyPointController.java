package com.senox.realty.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.service.EnergyMeteringPointBusinessService;
import com.senox.realty.service.RealtyEnergyMeteringPointService;
import com.senox.realty.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022-11-2
 */
@Api(tags = "物业-集抄")
@RestController
@RequestMapping("/realty/energy")
@RequiredArgsConstructor
public class RealtyEnergyPointController extends BaseController {
    private final RealtyEnergyMeteringPointService realtyEnergyMeteringPointService;
    private final EnergyMeteringPointBusinessService realtyEnergyMeteringPointBusinessService;

    @ApiOperation("绑定计量设备")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/bind")
    public void meteringPointBindRealty(@Validated({Add.class}) @RequestBody RealtyBindEnergyMeteringPointVo realtyBindPoint) {
        realtyEnergyMeteringPointBusinessService.meteringPointBindRealty(realtyBindPoint, true);
    }

    @ApiOperation("物业计量点统计")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/list/count")
    public Integer meteringPointCountList(@RequestBody RealtyToEnergyMeteringPointSearchVo search) {
        return realtyEnergyMeteringPointService.meteringPointToRealtyCountList(search);
    }

    @ApiOperation("物业计量点列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/list")
    public PageResult<RealtyToEnergyMeteringPointVo> meteringPointList(@RequestBody RealtyToEnergyMeteringPointSearchVo search) {
        return realtyEnergyMeteringPointService.meteringPointToRealtyPageList(search);
    }

    @ApiOperation("物业计量点读数列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/readings/list")
    public PageResult<RealtyToEnergyMeteringPointReadingsVo> meteringPointReadingsList(@RequestBody RealtyToEnergyMeteringPointReadingsSearchVo search) {
        return realtyEnergyMeteringPointService.meteringPointReadingsToRealtyPageList(search);
    }

    @ApiOperation("同步计量点读数")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/readings/sync")
    public void syncMeteringPoint(@RequestBody RealtyWeBatchVo realtyWeBatch) {
        realtyEnergyMeteringPointService.syncMeteringPoint(realtyWeBatch);
    }

    @ApiOperation("自动绑定计量设备")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/metering/point/automatic/bind")
    public PointBindRealtyResult automaticMeteringPointBindRealty(@RequestBody RealtyToEnergyMeteringPointSearchVo search) {
        return realtyEnergyMeteringPointBusinessService.automaticMeteringPointBindRealty(search);
    }

    @ApiOperation("计量点作废")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/metering/point/cancel/{meteringPointCode}")
    public void meteringPointCancel(@PathVariable String meteringPointCode) {
        realtyEnergyMeteringPointBusinessService.meteringPointCancel(meteringPointCode);
    }
}

package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.convert.FirefightingFileConvertor;
import com.senox.realty.domain.FirefightingFile;
import com.senox.realty.domain.FirefightingFileDetail;
import com.senox.realty.domain.FirefightingInspectMedia;
import com.senox.realty.domain.FirefightingInspectRealty;
import com.senox.realty.service.FirefightingFileDetailService;
import com.senox.realty.service.FirefightingFileService;
import com.senox.realty.service.FirefightingInspectMediaService;
import com.senox.realty.service.FirefightingInspectRealtyService;
import com.senox.realty.vo.FirefightingFileBriefVo;
import com.senox.realty.vo.FirefightingFileSearchVo;
import com.senox.realty.vo.FirefightingFileVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/19 15:12
 */
@Api(tags = "店铺消防安全档案")
@RestController
@RequiredArgsConstructor
@RequestMapping("/firefighting/file")
public class FirefightingFileController extends BaseController {

    private final FirefightingFileService fileService;
    private final FirefightingFileDetailService fileDetailService;
    private final FirefightingInspectRealtyService inspectRealtyService;
    private final FirefightingInspectMediaService inspectMediaService;
    private final FirefightingFileConvertor fileConvertor;


    @ApiOperation("添加店铺消防安全档案")
    @PostMapping("/add")
    public Long addFile(@Validated(Add.class) @RequestBody FirefightingFileVo file) {
        FirefightingFile entity = fileConvertor.toDo(file);
        FirefightingFileDetail detail = fileConvertor.toDetailDo(file);

        initEntityCreator(entity);
        initEntityModifier(entity);
        return fileService.addFile(file.getTaskId(), entity, detail, file.getRealtySerials(), file.getReinspectMedias());
    }

    @ApiOperation("更新店铺消防安全档案")
    @PostMapping("/update")
    public void updateFile(@Validated(Update.class) @RequestBody FirefightingFileVo file) {
        FirefightingFile f = fileConvertor.toDo(file);
        FirefightingFileDetail detail = fileConvertor.toDetailDo(file);

        initEntityModifier(f);
        fileService.updateFile(f, detail, file.getRealtySerials(), file.getReinspectMedias());
    }

    @ApiOperation("删除店铺消防安全档案")
    @PostMapping("/delete/{id}")
    public void deleteFile(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        fileService.deleteFile(id);
    }

    @ApiOperation("获取店铺消防安全档案详情")
    @GetMapping("/get/{id}")
    public FirefightingFileVo findFileById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        FirefightingFile file = fileService.findById(id);
        FirefightingFileDetail detail = fileDetailService.findById(id);
        FirefightingFileVo result = fileToVo(file, detail);
        if (result != null) {
            List<FirefightingInspectRealty> realtyList = inspectRealtyService.findByInspectId(id, InspectionType.FILE);
            result.setRealtySerials(realtyList.stream().map(FirefightingInspectRealty::getRealtySerial).collect(Collectors.toList()));

            List<FirefightingInspectMedia> medias = inspectMediaService.findByInspectId(id, InspectionType.FILE);
            if (!CollectionUtils.isEmpty(medias)) {
                List<String> reinspectMedias = medias.stream()
                        .filter(x -> x.getInspectTimes() > 1)
                        .map(FirefightingInspectMedia::getMediaUrl)
                        .collect(Collectors.toList());
                result.setReinspectMedias(reinspectMedias);
            }
        }
        return result;
    }

    @ApiOperation("店铺消防安全档案统计")
    @PostMapping("/count")
    public int countFile(@RequestBody FirefightingFileSearchVo search) {
        return fileService.countFile(search);
    }

    @ApiOperation("店铺消防安全档案列表")
    @PostMapping("/page")
    public PageResult<FirefightingFileBriefVo> listFileBriefPage(@RequestBody FirefightingFileSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> fileService.countFile(search), () -> fileService.listFile(search));
    }


    /**
     * 店铺消防安全档案转视图
     * @param file
     * @param detail
     * @return
     */
    private FirefightingFileVo fileToVo(FirefightingFile file, FirefightingFileDetail detail) {
        if (file == null) {
            return null;
        }
        FirefightingFileVo result = fileConvertor.toVo(file);
        if (detail != null) {
            result.setVenueDescription(detail.getVenueDescription());
            result.setFollowUp(detail.getFollowUp());
        }
        return result;
    }
}

package com.senox.realty.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.realty.convert.RealtyAliasConvertor;
import com.senox.realty.convert.RealtyConvertor;
import com.senox.realty.domain.Realty;
import com.senox.realty.domain.RealtyAlias;
import com.senox.realty.domain.RealtyExt;
import com.senox.realty.service.RealtyAliasService;
import com.senox.realty.service.RealtyService;
import com.senox.realty.vo.RealtyAliasVo;
import com.senox.realty.vo.RealtyReadingsVo;
import com.senox.realty.vo.RealtySearchVo;
import com.senox.realty.vo.RealtyTaxRateVo;
import com.senox.realty.vo.RealtyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/22 8:15
 */
@Api(tags = "物业管理")
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/realty")
public class RealtyController extends BaseController {

    private final RealtyService realtyService;
    private final RealtyAliasService realtyAliasService;
    private final RealtyConvertor realtyConvertor;
    private final RealtyAliasConvertor realtyAliasConvertor;

    @ApiOperation("新增物业信息")
    @PostMapping("/add")
    public Long addRealty(@Validated({Add.class}) @RequestBody RealtyVo realty) {
        Realty entity = realtyConvertor.toDo(realty);
        RealtyExt extEntity = realtyConvertor.toExtDo(realty);

        initEntityCreator(entity);
        initEntityModifier(entity);
        return realtyService.addRealty(entity, extEntity);
    }

    @ApiOperation("更新物业信息")
    @PostMapping("/update")
    public void updateRealty(@Validated({Update.class}) @RequestBody RealtyVo realty) {
        if (realty.getId() < 1L) {
            throw new InvalidParameterException("无效id");
        }

        Realty entity = realtyConvertor.toDo(realty);
        RealtyExt extEntity = realtyConvertor.toExtDo(realty);

        initEntityModifier(entity);
        realtyService.updateRealty(entity, extEntity);
    }


    @ApiOperation("获取物业信息")
    @GetMapping("/get/{id}")
    public RealtyVo getRealty(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }

        Realty realty = realtyService.findById(id);
        return realty == null ? null : realtyConvertor.toVo(realty);
    }

    @ApiOperation("根据物业编号获取物业")
    @GetMapping("/getBySerial")
    public RealtyVo getRealtyBySerial(@RequestParam String serial) {
        if (StringUtils.isBlank(serial)) {
            throw new InvalidParameterException("无效的物业编号");
        }

        Realty realty = realtyService.findBySerial(serial);
        return realty == null ? null : realtyConvertor.toVo(realty);
    }

    @ApiOperation("获取物业及业主信息")
    @GetMapping("/getWithOwner/{id}")
    public RealtyVo getRealtyWithOwner(@PathVariable Long id) {
        if (id < 1L) {
            throw new InvalidParameterException("无效的id");
        }

        RealtyVo result = realtyService.findWithOwnerById(id);
        if (result != null) {
            RealtyExt ext = realtyService.findExtByRealtyId(id);
            if (ext != null) {
                result.setWaterReadings(ext.getWaterReadings());
                result.setElectricReadings(ext.getElectricReadings());
                result.setElectricPrice(ext.getElectricPrice());
                result.setWaterPrice(ext.getWaterPrice());
                result.setRemark(ext.getRemark());
                result.setRentTaxCode(ext.getRentTaxCode());
            }
        }
        return result;
    }

    @ApiOperation("物业信息列表")
    @PostMapping("/list")
    public PageResult<RealtyVo> listRealtyPage(@RequestBody RealtySearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }

        return PageUtils.commonPageResult(searchVo, () -> realtyService.countRealty(searchVo), () -> {
            List<Realty> list = realtyService.listRealty(searchVo);
            return realtyConvertor.toVo(list);
        });
    }

    @ApiOperation("新增物业别名")
    @PostMapping("/alias/save/{realtyId}")
    public void addRealtyAlias(@PathVariable Long realtyId, @Validated @RequestBody List<RealtyAliasVo> aliasList) {
        if (!WrapperClassUtils.biggerThanLong(realtyId, 0L)) {
            throw new InvalidParameterException();
        }

        List<RealtyAlias> list = realtyAliasConvertor.toDo(aliasList);
        list.forEach(x -> {
            x.setRealtyId(realtyId);
            if (x.getWaterReadings() == null) {
                x.setWaterReadings(0L);
            }
            if (x.getElectricReadings() == null) {
                x.setElectricReadings(0L);
            }
            initEntityCreator(x);
            initEntityModifier(x);
            x.setCreateTime(LocalDateTime.now());
            x.setModifiedTime(LocalDateTime.now());
        });
        realtyAliasService.batchSaveRealtyAlias(realtyId, list);
    }

    @ApiOperation("获取物业别名")
    @GetMapping("/alias/list/{realtyId}")
    public List<RealtyAliasVo> listRealtyAlias(@PathVariable Long realtyId) {
        if (!WrapperClassUtils.biggerThanLong(realtyId, 0L)) {
            throw new InvalidParameterException();
        }

        return realtyAliasConvertor.toV(realtyAliasService.listRealtyAlias(realtyId));
    }

    @ApiOperation("物业水电读数")
    @GetMapping("/we/{realtyId}")
    public List<RealtyReadingsVo> listLatestReadings(@PathVariable Long realtyId) {
        if (!WrapperClassUtils.biggerThanLong(realtyId, 0L)) {
            throw new InvalidParameterException();
        }

        return realtyService.listReadings(realtyId);
    }

    @ApiOperation("更新物业水电读数")
    @PostMapping("/we/update")
    public void updateReadings(@Valid @RequestBody List<RealtyReadingsVo> list) {
        realtyService.updateRealtyReadings(list);
    }

    @ApiOperation("添加物业税率")
    @PostMapping("/taxRate/save")
    public void saveRealtyTaxRate(@RequestBody RealtyTaxRateVo realtyTaxRate) {
        realtyService.saveRealtyTaxRate(realtyTaxRate);
    }

    @ApiOperation("取消物业税率")
    @PostMapping("/taxRate/cancel")
    public void cancelRealtyTaxRate(@RequestBody RealtyTaxRateVo realtyTaxRate) {
        realtyService.cancelRealtyTaxRate(realtyTaxRate);
    }

    @ApiOperation("物业税率分页列表")
    @PostMapping("/taxRate/list/page")
    public PageResult<RealtyVo> pageListRealtyTaxRate(@RequestBody RealtySearchVo search) {
        return realtyService.pageListRealtyTaxRate(search);
    }

}

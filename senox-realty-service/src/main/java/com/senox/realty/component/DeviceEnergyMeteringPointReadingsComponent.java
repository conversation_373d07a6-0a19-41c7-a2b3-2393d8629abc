package com.senox.realty.component;

import com.senox.common.utils.FeignUtils;
import com.senox.dm.api.clients.EnergyClient;
import com.senox.common.constant.device.EnergyType;
import com.senox.dm.dto.EnergyMeteringPointReadingsDto;
import com.senox.dm.vo.HolleyPointMeterQueryBatchRequest;
import com.senox.realty.domain.EnergyMeteringPointReadings;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-11
 **/
@RequiredArgsConstructor
@Component
public class DeviceEnergyMeteringPointReadingsComponent {
    private final EnergyClient energyClient;

    /**
     * 远程计量点读数列表
     * @param energyType 能源类型
     * @param batchReq 请求参数
     * @return 返回读数列表
     */
    public List<EnergyMeteringPointReadings> remoteList(EnergyType energyType, HolleyPointMeterQueryBatchRequest batchReq) {
        try {
            List<EnergyMeteringPointReadingsDto> meteringpointReadingsDtoList = energyClient.remoteMeteringPointReadingsList(energyType, batchReq);
            if (CollectionUtils.isEmpty(meteringpointReadingsDtoList)) {
                return Collections.emptyList();
            }
            List<EnergyMeteringPointReadings> meteringPointReadingsList = new ArrayList<>(meteringpointReadingsDtoList.size());
            for (EnergyMeteringPointReadingsDto meteringPointReadingsDto : meteringpointReadingsDtoList) {
                EnergyMeteringPointReadings meteringPointReadings = new EnergyMeteringPointReadings();
                meteringPointReadings.setRtuCode(meteringPointReadingsDto.getRtuCode());
                meteringPointReadings.setPointCode(meteringPointReadingsDto.getPointCode());
                meteringPointReadings.setReadings(meteringPointReadingsDto.getReadings());
                meteringPointReadings.setDataTime(meteringPointReadingsDto.getDataTime());
                meteringPointReadings.setGrabTime(LocalDateTime.now());
                meteringPointReadingsList.add(meteringPointReadings);
            }
            return meteringPointReadingsList;
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }
}

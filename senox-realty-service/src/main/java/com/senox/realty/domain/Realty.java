package com.senox.realty.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 物业
 * <AUTHOR>
 * @Date 2020/12/16 8:33
 */
@Getter
@Setter
@ToString
@TableName("r_realty")
public class Realty extends TableIdEntity {

    private static final long serialVersionUID = 2419075173854828910L;

    /**
     * 编号
     */
    private String serialNo;
    /**
     * 物业名
     */
    private String name;
    /**
     * 性质
     */
    private Integer nature;
    /**
     * 面积
     */
    private BigDecimal area;
    /**
     * 区域id
     */
    private Long regionId;
    /**
     * 区域名
     */
    private String regionName;
    /**
     * 街道id
     */
    private Long streetId;
    /**
     * 街道名
     */
    private String streetName;
    /**
     * 地址
     */
    private String address;
    /**
     * 行业id
     */
    private Long professionId;
    /**
     * 行业名称
     */
    private String professionName;
    /**
     * 业主id
     */
    private Long ownerId;
    /**
     * 业主名
     */
    private String ownerName;
    /**
     * 排序号
     */
    private Integer orderNum;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Realty realty = (Realty) o;
        return Objects.equals(serialNo, realty.serialNo)
                && Objects.equals(name, realty.name)
                && Objects.equals(nature, realty.nature)
                && Objects.equals(area, realty.area)
                && Objects.equals(regionId, realty.regionId)
                && Objects.equals(regionName, realty.regionName)
                && Objects.equals(streetId, realty.streetId)
                && Objects.equals(streetName, realty.streetName)
                && Objects.equals(address, realty.address)
                && Objects.equals(professionId, realty.professionId)
                && Objects.equals(professionName, realty.professionName)
                && Objects.equals(ownerId, realty.ownerId)
                && Objects.equals(ownerName, realty.ownerName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(serialNo, name, nature, area, regionId, regionName, streetId, streetName, address,
                professionId, professionName, ownerId, ownerName);
    }

}

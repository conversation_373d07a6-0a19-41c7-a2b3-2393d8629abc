package com.senox.realty.mapper;

import com.senox.common.vo.BillPaidVo;
import com.senox.realty.domain.RealtyDeposit;
import com.senox.realty.vo.BillTollVo;
import com.senox.realty.vo.RealtyDepositSearchVo;
import com.senox.realty.vo.RealtyDepositVo;
import com.senox.realty.vo.TollSerialVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/3 8:16
 */
@Mapper
@Repository
public interface RealtyDepositMapper {

    /**
     * 添加物业押金
     * @param deposit
     * @return
     */
    int addDeposit(RealtyDeposit deposit);

    /**
     * 批量添加物业押金
     * @param depositList
     * @return
     */
    int batchAddDeposit(@Param("list") List<RealtyDeposit> depositList);

    /**
     * 更新物业押金
     * @param deposit
     * @return
     */
    int updateDeposit(RealtyDeposit deposit);

    /**
     * 根据押金单id更新押金状态
     * @param billPaid
     * @return
     */
    int updateDepositPaidById(BillPaidVo billPaid);

    /**
     * 根据支付订单id更新押金账单状态
     * @param billPaid
     * @return
     */
    int updateDepositPaidByRemoteOrder(BillPaidVo billPaid);

    /**
     * 通过账单id押金退费
     * @param billPaid
     * @return
     */
    int updateDepositRefundById(BillPaidVo billPaid);

    /**
     * 通过支付订单id押金退费
     * @param billPaid
     * @return
     */
    int updateDepositRefundByRemoteOrder(BillPaidVo billPaid);

    /**
     * 撤销押金收费
     * @param billToll
     * @return
     */
    int revokeDepositPaid(BillTollVo billToll);

    /**
     * 更新物业押金票据号
     * @param serial
     * @return
     */
    int updateDepositTollSerial(TollSerialVo serial);

    /**
     * 支付物业押金
     * @param toll
     * @return
     */
    int payDeposit(BillTollVo toll);

    /**
     * 物业押金退费
     * @param toll
     * @return
     */
    int refundDeposit(BillTollVo toll);

    /**
     * 撤销押金退费
     * @param toll
     * @return
     */
    int revokeDepositRefund(BillTollVo toll);

    /**
     * 根据id获取物业押金记录
     * @param id
     * @return
     */
    RealtyDepositVo findById(Long id);

    /**
     * 根据id列表获取物业押金记录
     * @param ids
     * @return
     */
    List<RealtyDepositVo> listByIds(List<Long> ids);

    /**
     * 合同押金
     * @param contractId
     * @return
     */
    List<RealtyDepositVo> listContractDeposit(Long contractId);

    /**
     * 合计物业押金记录
     * @param search
     * @return
     */
    RealtyDepositVo sumDeposit(RealtyDepositSearchVo search);

    /**
     * 合计物业押金记录
     * @param search
     * @return
     */
    int countDeposit(RealtyDepositSearchVo search);

    /**
     * 物业押金记录列表
     * @param search
     * @return
     */
    List<RealtyDepositVo> listDeposit(RealtyDepositSearchVo search);

}

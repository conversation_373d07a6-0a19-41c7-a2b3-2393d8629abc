package com.senox.realty.mapper;

import com.senox.context.AdminUserDto;
import com.senox.realty.domain.RealtyExt;
import com.senox.realty.vo.RealtyEnergyVo;
import com.senox.realty.vo.RealtyReadingsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/1 9:38
 */
@Mapper
@Repository
public interface RealtyExtMapper {

    /**
     * 添加物业扩展信息
     * @param ext
     * @return
     */
    int addRealtyExt(RealtyExt ext);

    /**
     * 更新物业扩展信息
     * @param ext
     * @return
     */
    int updateRealtyExt(RealtyExt ext);

    /**
     * 更新物业扩展信息税率
     * @param realtyIds 物业id集
     * @param taxCode 税收编码
     * @param user 当前用户
     */
    void updateRealtyExtTaxRate(List<Long> realtyIds, String taxCode, AdminUserDto user);

    /**
     * 更新物业能源价格
     * @param contractId
     * @param ext
     * @return
     */
    int updateEnergyPriceByContractId(@Param("contractId") Long contractId, @Param("ext") RealtyExt ext);

    /**
     * 更新水电读数
     * @param list
     * @return
     */
    int batchUpdateRealtyReadings(@Param("list") List<RealtyReadingsVo> list);

    /**
     * 根据物业id获取扩展信息
     * @param realtyId
     * @return
     */
    RealtyExt findByRealtyId(Long realtyId);

    /**
     * 根据物业编号获取物业能源信息
     * @param realtySerials
     * @return
     */
    List<RealtyEnergyVo> listEnergyBySerials(@Param("realtySerials") List<String> realtySerials);

    /**
     * 根据子表编号获取物业能源信息
     * @param realtySerials
     * @return
     */
    List<RealtyEnergyVo> listEnergyBySubSerials(@Param("realtySerials") List<String> realtySerials);
}

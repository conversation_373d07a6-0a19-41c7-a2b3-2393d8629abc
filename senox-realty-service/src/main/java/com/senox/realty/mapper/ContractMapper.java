package com.senox.realty.mapper;

import com.senox.realty.domain.Contract;
import com.senox.realty.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/8 15:05
 */
@Mapper
@Repository
public interface ContractMapper {

    /**
     * 新建合同
     * @param contract
     * @return
     */
    int addContract(Contract contract);

    /**
     * 更新合同
     * @param contract
     * @return
     */
    int updateContract(Contract contract);

    /**
     * 启用合同
     * @param enableDto
     * @return
     */
    int enableContract(ContractEnableDto enableDto);

    /**
     * 停用合同
     * @param suspendDto
     * @return
     */
    int suspendByContractNo(RealtyContractSuspendDto suspendDto);

    /**
     * 停用合同
     * @param suspendDto
     * @return
     */
    int suspendByRealtyAndType(RealtyContractSuspendDto suspendDto);

    /**
     * 根据id获取合同
     * @param id
     * @return
     */
    Contract findById(Long id);

    /**
     * 根据合同号获取合同
     * @param contractNo
     * @return
     */
    Contract findByContractNo(String contractNo);

    /**
     * 根据合同号批量获取合同
     *
     * @param contractNos 合同号列表
     * @return 合同列表
     */
    List<Contract> listByContractNos(List<String> contractNos);


    /**
     * 按类型查找物业合同
     * @param id
     * @param realtyId
     * @param type
     * @return
     */
    Contract findRealtyLastContract(@Param("id") Long id,
                                    @Param("realtyId") Long realtyId,
                                    @Param("type") Integer type);

    /**
     * 查找合同信息
     * @param contractNo
     * @return
     */
    ContractVo findWithExtByContractNo(String contractNo);


    /**
     * 查找物业最大可租赁日期
     * @param realtyId
     * @return
     */
    LocalDate findRealtyMaxRentableDate(Long realtyId);

    /**
     * 根据物业id获取最新合同
     * @param realtyId
     * @param date
     * @return
     */
    List<Contract> listAvailableContractByRealtyId(@Param("realtyId") Long realtyId, @Param("date") LocalDate date);

    /**
     * 根据id获取合同及物业、客户信息
     * @param id
     * @return
     */
    ContractVo findWithRealtyAndCustomerById(Long id);

    /**
     * 查续签源
     * @param id
     * @return
     */
    ContractVo findRenewFrom(Long id);

    /**
     * 获取最大合同号
     * @param prefix
     * @return
     */
    String findMaxContractNo(String prefix);

    /**
     * 合同统计
     * @param search
     * @return
     */
    int countContract(ContractSearchVo search);

    /**
     * 合同列表
     * @param search
     * @return
     */
    List<ContractVo> listContract(ContractSearchVo search);

    /**
     * 账单合同
     * @param search
     * @return
     */
    List<ContractVo> listBillContract(ContractBillSearchVo search);

    /**
     * 合同银行信息列表
     * @param search
     * @return
     */
    List<ContractBankVo> listContractBank(ContractSearchVo search);

    /**
     * 租赁合同统计
     * @param search
     * @return
     */
    int countLeaseContract(ContractSearchVo search);

    /**
     * 租赁合同列表
     * @param search
     * @return
     */
    List<LeaseContractListVo> listLeaseContract(ContractSearchVo search);

}

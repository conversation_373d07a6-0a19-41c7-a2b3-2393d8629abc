package com.senox.realty.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.api.clients.ReceiptApplyClient;
import com.senox.pm.api.clients.ReceiptClient;
import com.senox.pm.constant.ReceiptEnum;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.pm.constant.ReceiptType;
import com.senox.pm.constant.TaxCategory;
import com.senox.pm.dto.ReceiptApplyDto;
import com.senox.pm.dto.ReceiptApplyItemDto;
import com.senox.pm.vo.ReceiptApplySearchVo;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.pm.vo.TaxHeaderVo;
import com.senox.realty.config.GoodsData;
import com.senox.realty.config.ReceiptConfig;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.ContractType;
import com.senox.realty.constant.RealtyFee;
import com.senox.realty.domain.Contract;
import com.senox.realty.domain.Realty;
import com.senox.realty.domain.RealtyBillItem;
import com.senox.realty.domain.RealtyReceipt;
import com.senox.realty.dto.BillReceiptApplyDto;
import com.senox.realty.dto.RealtyReceiptItemDto;
import com.senox.realty.event.RabbitSendEvent;
import com.senox.realty.event.RealtyBillReceiptEvent;
import com.senox.realty.mapper.RealtyBillMapper;
import com.senox.realty.mapper.RealtyReceiptMapper;
import com.senox.realty.service.ContractService;
import com.senox.realty.service.RealtyBillService;
import com.senox.realty.service.RealtyReceiptService;
import com.senox.realty.service.RealtyService;
import com.senox.realty.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.senox.realty.constant.RealtyConst.Cache.*;
import static com.senox.realty.constant.RealtyConst.MQ.*;

/**
 * 物业发票服务实现
 *
 * <AUTHOR>
 * @date 2023-3-23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RealtyReceiptServiceImpl implements RealtyReceiptService {

    private final RealtyBillService realtyBillService;
    private final ContractService contractService;
    private final RealtyService realtyService;
    private final RealtyReceiptMapper realtyReceiptMapper;
    private final RealtyBillMapper billMapper;
    private final ReceiptClient receiptClient;
    private final ReceiptApplyClient receiptApplyClient;
    private final ReceiptConfig receiptConfig;
    private final RabbitTemplate rabbitTemplate;
    private final ApplicationEventPublisher publisher;

    @Value("#{'${senox.realty.rent-anyway:}'.split(',')}")
    private List<String> rentAnywayRealties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void apply(RealtyReceiptMangerVo realtyReceiptManger) {
        if (null == realtyReceiptManger
                || StringUtils.isBlank(realtyReceiptManger.getOpenid())
                || !WrapperClassUtils.biggerThanLong(realtyReceiptManger.getTaxHeaderId(), 0)
                || CollectionUtils.isEmpty(realtyReceiptManger.getBillReceiptList())
        ) {
            return;
        }
        if (!RedisUtils.lock(String.format(KEY_RECEIPT_LOCK, realtyReceiptManger.getOpenid()), TTL_1H)) {
            throw new BusinessException("请勿频繁申请");
        }
        try {
            log.info("【Receipt】 wechat发票申请,data:{}", JsonUtils.object2Json(realtyReceiptManger));
            //开票申请
            receiptApply(realtyReceiptManger, true, true, true);
        } finally {
            RedisUtils.del(String.format(KEY_RECEIPT_LOCK, realtyReceiptManger.getOpenid()));
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pcApply(RealtyReceiptMangerVo realtyReceiptManger) {
        if (null == realtyReceiptManger
                || !WrapperClassUtils.biggerThanLong(realtyReceiptManger.getTaxHeaderId(), 0)
                || CollectionUtils.isEmpty(realtyReceiptManger.getBillReceiptList())
        ) {
            return;
        }
        realtyReceiptManger.getBillReceiptList().forEach(billReceipt -> {
            if (!RedisUtils.lock(String.format(KEY_RECEIPT_BILL_LOCK, billReceipt.getBillId()), TTL_1H)) {
                throw new BusinessException("请勿频繁申请");
            }
        });
        try {
            log.info("【Receipt】 web发票申请,data:{}", JsonUtils.object2Json(realtyReceiptManger));
            //开票申请
            receiptApply(realtyReceiptManger, false, false, false);
        } finally {
            realtyReceiptManger.getBillReceiptList().forEach(billReceipt -> RedisUtils.del(String.format(KEY_RECEIPT_BILL_LOCK, billReceipt.getBillId())));
        }
    }

    @Override
    public RealtyReceipt findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return realtyReceiptMapper.findById(id);
    }

    @Override
    public void update(RealtyReceipt realtyReceipt) {
        if (null == realtyReceipt) {
            return;
        }
        updateBatch(Collections.singletonList(realtyReceipt));
    }

    @Override
    public void updateBatch(List<RealtyReceipt> realtyReceiptList) {
        if (CollectionUtils.isEmpty(realtyReceiptList)) {
            return;
        }
        realtyReceiptMapper.updateBatch(realtyReceiptList);
    }

    @Override
    public List<RealtyReceiptVo> list(RealtyReceiptSearchVo search) {
        search.setPage(false);
        return realtyReceiptMapper.list(search);
    }

    @Override
    public PageResult<RealtyReceiptVo> listPage(RealtyReceiptSearchVo search) {
        return PageUtils.commonPageResult(search, () -> realtyReceiptMapper.count(search), () -> realtyReceiptMapper.list(search));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(Long id, ReceiptStatus status, String remark, Long auditMan) {
        RealtyReceipt realtyReceipt = findById(id);
        if (null == realtyReceipt) {
            throw new BusinessException("未找到物业发票申请!");
        }
        List<String> receiptSerialNoList = receiptSerialNoListById(realtyReceipt.getId());
        if (CollectionUtils.isEmpty(receiptSerialNoList)) {
            throw new BusinessException("未找到发票申请!");
        }
        realtyReceipt.setApplyStatus(status.getStatus());
        realtyReceipt.setAuditRemark(remark);
        realtyReceipt.setAuditMan(auditMan);
        //审核通过
        if (status.equals(ReceiptStatus.AUDIT_APPROVED)) {
            publisher.publishEvent(new RabbitSendEvent(this, EX_RECEIPT_APPLY_PASS, MQ_RECEIPT_APPLY_PASS, receiptSerialNoList));
        }
        //审核不通过
        if (status.equals(ReceiptStatus.AUDIT_REJECTED)) {
            //重置开票状态
            realtyBillReceiptReset(realtyReceipt.getId(), receiptSerialNoList);
            ReceiptApplySearchVo applySearch = new ReceiptApplySearchVo();
            applySearch.setPage(false);
            applySearch.setSerialNoList(receiptSerialNoList);
            PageResult<ReceiptApplyVo> result = receiptApplyClient.list(applySearch);
            List<ReceiptApplyVo> applyVoList = result.getDataList().stream().map(a -> {
                ReceiptApplyVo apply = new ReceiptApplyVo();
                apply.setStatus(ReceiptStatus.AUDIT_REJECTED.getStatus());
                apply.setSerialNo(a.getSerialNo());
                return apply;
            }).collect(Collectors.toList());
            publisher.publishEvent(new RabbitSendEvent(this, EX_RECEIPT_APPLY_STATUS, MQ_RECEIPT_APPLY_STATUS, applyVoList));
        }
        update(realtyReceipt);
    }

    @Override
    public List<String> receiptSerialNoListById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return Collections.emptyList();
        }
        return realtyReceiptMapper.receiptSerialNoListById(Collections.singletonList(id));
    }

    @Override
    public List<RealtyReceipt> getBySerialNoList(List<String> serialNoList) {
        if (CollectionUtils.isEmpty(serialNoList)) {
            return Collections.emptyList();
        }
        return realtyReceiptMapper.getBySerialNoList(serialNoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBillItemReceiptStatus(List<ReceiptApplyVo> applyList) {
        if (CollectionUtils.isEmpty(applyList)) {
            return;
        }
        //只接收开票成功的
        Map<String, ReceiptApplyVo> applyMap = applyList
                .stream()
                .filter(apply -> ReceiptStatus.fromType(apply.getStatus()).equals(ReceiptStatus.ISSUED))
                .collect(Collectors.toMap(ReceiptApplyVo::getSerialNo, Function.identity()));
        if (applyMap.isEmpty()) {
            return;
        }
        List<RealtyBillItem> billItemList = billMapper.listBillItemByReceiptSerial(new ArrayList<>(applyMap.keySet()));
        List<RealtyBillItem> billItemUpdateList = new ArrayList<>();
        billItemList.forEach(item -> {
            ReceiptApplyVo receiptApply = applyMap.get(item.getReceiptSerialNo());
            ReceiptStatus receiptStatus = ReceiptStatus.fromType(receiptApply.getStatus());
            //开具失败
            if (receiptStatus.equals(ReceiptStatus.ISSUE_FAILED)) {
                item.setReceiptStatus(ReceiptStatus.PENDING_ISSUE.getStatus());
            }
            //开具成功
            if (receiptStatus.equals(ReceiptStatus.ISSUED)) {
                item.setReceiptStatus(ReceiptStatus.ISSUED.getStatus());
            }
            item.setModifierId(receiptApply.getReceiptMan());
            item.setModifierName(receiptApply.getReceiptManName());
            billItemUpdateList.add(item);
        });
        realtyBillService.batchUpdateBillItemReceipt(billItemUpdateList);
        publisher.publishEvent(new RealtyBillReceiptEvent(publisher, billItemUpdateList));
    }

    @Override
    public List<ReceiptApplyVo> receiptApplyListByRealtyReceiptId(Long id, Boolean isDetail) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return Collections.emptyList();
        }
        return realtyReceiptMapper.receiptApplyListByRealtyReceiptId(id, isDetail);
    }


    @Override
    public List<RealtyBillReceiptApplyInfoVo> applyBillInfoList(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return Collections.emptyList();
        }
        return realtyReceiptMapper.applyBillInfoList(id);
    }

    @Override
    public void refreshBillReceiptStatus(List<Long> billIds) {
        realtyReceiptMapper.refreshBillReceiptStatus(billIds);
    }

    /**
     * 构建发票申请
     *
     * @param billReceiptManger 物业账单发票管理
     * @param taxHeader         发票抬头
     */
    private ReceiptApplyDto buildReceiptApply(RealtyReceiptMangerVo billReceiptManger, TaxHeaderVo taxHeader, GoodsData goodsData) {
        if (null == taxHeader) {
            return null;
        }
        if (null == ReceiptType.fromType(billReceiptManger.getReceiptType().getType())) {
            throw new BusinessException("发票类型错误!");
        }
        ReceiptApplyDto receiptApply = new ReceiptApplyDto();
        receiptApply.setSource("realty");
        receiptApply.setSerialNumber("05");
        TaxCategory category = TaxCategory.fromValue(taxHeader.getCategory());
        receiptApply.setTaxHeader(category == TaxCategory.PERSONAL ? taxHeader.getHeader().concat("(个人)") : taxHeader.getHeader());
        receiptApply.setOpenid(taxHeader.getOpenid());
        receiptApply.setHeaderCategory(taxHeader.getCategory());
        receiptApply.setReceiptType(billReceiptManger.getReceiptType().getType());
        receiptApply.setConstraintType(ReceiptEnum.ConstraintType.NORMAL.getType());
        receiptApply.setMobile(billReceiptManger.getMobile());
        receiptApply.setEmail(billReceiptManger.getPurchaseEmail());
        if (null != goodsData.getConstraintType()) {
            receiptApply.setConstraintType(goodsData.getConstraintType().getType());
        }

        if (TaxCategory.fromValue(receiptApply.getHeaderCategory()).equals(TaxCategory.PERSONAL)) {
            return receiptApply;
        }

        if (billReceiptManger.getReceiptType().equals(ReceiptType.NORMAL)) {
            if (StringUtils.isBlank(taxHeader.getSerial())) {
                throw new BusinessException("纳税人识别号缺失!");
            }
            receiptApply.setTaxSerial(taxHeader.getSerial());
        }

        if (billReceiptManger.getReceiptType().equals(ReceiptType.SPECIAL)) {
            if (!taxHeader.checkEnterpriseInfo()) {
                throw new BusinessException("抬头信息缺失!");
            }
            receiptApply.setTaxSerial(taxHeader.getSerial());
            receiptApply.setRegisterAddress(taxHeader.getRegisterAddress());
            receiptApply.setRegisterMobile(taxHeader.getRegisterMobile());
            receiptApply.setDepositBank(taxHeader.getDepositBank());
            receiptApply.setEnterpriseBankAccount(taxHeader.getEnterpriseBankAccount());
        }


        return receiptApply;
    }

    /**
     * 检查账单
     *
     * @param realtyBillList 账单列表
     * @param checkDate      检查时间
     * @param checkPaid      检查已收
     */
    private void checkBill(List<RealtyBillVo> realtyBillList, boolean checkDate, boolean checkPaid) {
        if (CollectionUtils.isEmpty(realtyBillList)) {
            return;
        }
        if (!checkDate) {
            return;
        }

        //校验账单
        realtyBillList.forEach(r -> {
            //日期检查
            if (LocalDate.of(r.getBillYear(), r.getBillMonth(), 1).until(LocalDate.now(), ChronoUnit.MONTHS) >= 3) {
                throw new BusinessException(String.format("[%s]账单超过开票有效期", r.getId()));
            }
            //支付状态检查
            if (checkPaid && !r.getStatus().equals(BillStatus.PAID.getStatus())) {
                throw new BusinessException(String.format("[%s]账单未支付", r.getId()));
            }

        });

    }

    /**
     * 发票申请
     *
     * @param applyDto          账单发票申请dto
     * @param billReceiptManger 物业账单发票管理
     * @param realtyTaxRateMap  物业税率
     * @return 物业发票集合
     */
    private List<ReceiptApplyDto> saveReceiptApply(
            BillReceiptApplyDto applyDto,
            RealtyReceiptMangerVo billReceiptManger,
            Map<String, RealtyVo> realtyTaxRateMap
    ) {
        if (null == applyDto || null == billReceiptManger) {
            return Collections.emptyList();
        }
        Map<RealtyBillVo, List<RealtyBillItem>> billMap = applyDto.getBillMap();
        if (null == billMap || billMap.isEmpty()) {
            throw new BusinessException("未找到可开具费项!");
        }
        List<RealtyReceiptItemDto> itemDtoList = new ArrayList<>();
        billMap.forEach((bill, billItems) -> {
            RealtyVo realty = realtyTaxRateMap.get(bill.getRealtySerial());
            for (RealtyBillItem billItem : billItems) {
                RealtyReceiptItemDto itemDto = new RealtyReceiptItemDto();
                String feeName = billItem.getFeeName();
                GoodsData goodsData = receiptConfig.goodsDataByName(feeName);
                //是租金且物业上的租金税收编码不为null的
                if (feeName.equals(RealtyFee.RENT.getName()) && (null != realty && !StringUtils.isBlank(realty.getRentTaxCode()))) {
                    goodsData = receiptConfig.goodsDataByTaxCode(realty.getRentTaxCode());
                }
                itemDto.setFeeName(feeName);
                itemDto.setGoodsData(goodsData);
                itemDto.setBillItem(billItem);
                itemDtoList.add(itemDto);
            }
        });
        Map<Integer, List<RealtyReceiptItemDto>> itemGroup = itemDtoList
                .stream()
                .collect(Collectors.groupingBy(i -> i.getFeeName().concat(i.getGoodsData().getTax().toString()).hashCode()));
        List<ReceiptApplyDto> receiptApplyList = new ArrayList<>();
        List<List<RealtyBillItem>> billItemUpdateAllList = new ArrayList<>();
        itemGroup.forEach((hash, items) -> {
            GoodsData goodsData = items.get(0).getGoodsData();
            //构建发票申请
            ReceiptApplyDto receiptApply = buildReceiptApply(billReceiptManger, applyDto.getTaxHeader(), goodsData);
            AtomicReference<BigDecimal> totalIncludedTaxUnitPrice = new AtomicReference<>(BigDecimal.ZERO);
            RealtyFee realtyFee = RealtyFee.fromFeeName(items.get(0).getFeeName());
            StringBuilder remarkBuilder = new StringBuilder();
            List<RealtyBillItem> billItemUpdateList = new ArrayList<>();
            for (RealtyReceiptItemDto itemDto : items) {
                RealtyBillItem item = itemDto.getBillItem();
                //计算金额
                totalIncludedTaxUnitPrice.updateAndGet(s -> s.add(item.getAmount()));
                //设置开票状态
                item.setReceiptStatus(ReceiptStatus.ISSUING.getStatus());
                item.setModifierId(AdminContext.getUser().getUserId());
                item.setModifierName(AdminContext.getUser().getUsername());
                RealtyBillVo bill = applyDto.getBillMap().keySet().stream().filter(b -> b.getId().equals(item.getBillId())).findAny().orElse(null);
                if (null == bill) {
                    throw new BusinessException(ResultConst.ERROR);
                }
                LocalDate billTime = LocalDate.of(bill.getBillYear(), bill.getBillMonth(), 1);
                if (realtyFee.equals(RealtyFee.WATER) || realtyFee.equals(RealtyFee.ELECTRIC)) {
                    remarkBuilder.append(item.getAttr1().concat("至").concat(item.getAttr2()));
                } else {
                    remarkBuilder.append(DateUtils.getFirstDateInMonth(billTime).toString().concat("至").concat(DateUtils.getLastDateInMonth(billTime).toString()));
                }
                remarkBuilder.append(",").append(bill.getRealtyName()).append("\n");
                //放入待更新列表
                billItemUpdateList.add(item);
            }
            billItemUpdateAllList.add(billItemUpdateList);
            //构建申请项
            ReceiptApplyItemDto receiptApplyItem = buildReceiptApplyItem(realtyFee.getName(), totalIncludedTaxUnitPrice.get(), goodsData);
            receiptApply.setReceiptApplyItemList(Lists.newArrayList(receiptApplyItem));
            if (realtyFee.equals(RealtyFee.RENT)) {
                LocalDate minTime = Instant.ofEpochMilli(applyDto.getMinTimeLong()).atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate maxTime = Instant.ofEpochMilli(applyDto.getMaxTimeLong()).atZone(ZoneId.systemDefault()).toLocalDate();
                remarkBuilder.insert(0, String.format("[%s,%s]%n", DateUtils.formatYearMonth(DateUtils.getFirstDateInMonth(minTime), DateUtils.PATTERN_FULL_DATE), DateUtils.formatYearMonth(DateUtils.getLastDateInMonth(maxTime), DateUtils.PATTERN_FULL_DATE)));
            }
            receiptApply.setRemark(remarkBuilder.toString());
            receiptApplyList.add(receiptApply);
        });
        if (CollectionUtils.isEmpty(receiptApplyList)) {
            return Collections.emptyList();
        }
        //发票申请添加
        List<String> serialNos = receiptApplyClient.add(receiptApplyList);
        //设置单据编号
        Deque<String> stringQueue = new ArrayDeque<>(serialNos);
        for (int i = 0; i < receiptApplyList.size(); i++) {
            ReceiptApplyDto receiptApply = receiptApplyList.get(i);
            receiptApply.setSerialNo(stringQueue.pop());
            billItemUpdateAllList.get(i).forEach(l -> l.setReceiptSerialNo(receiptApply.getSerialNo()));
        }
        //更新账单费项发票状态
        realtyBillService.batchUpdateBillItemReceipt(billItemUpdateAllList.stream().flatMap(List::stream).collect(Collectors.toList()));
        return receiptApplyList;
    }

    private ReceiptApplyItemDto buildReceiptApplyItem(String feeName, BigDecimal includedTaxUnitPrice, GoodsData goodsData) {
        ReceiptApplyItemDto receiptApplyItem = new ReceiptApplyItemDto();
        receiptApplyItem.setLinType(ReceiptEnum.LineType.NORMAL.getType());
        receiptApplyItem.setName(goodsData.getName());
        receiptApplyItem.setTitle(goodsData.getTitle());
        receiptApplyItem.setUnit(goodsData.getUnit());
        receiptApplyItem.setTax(goodsData.getTax());
        receiptApplyItem.setTaxCode(goodsData.getTaxCode());
        receiptApplyItem.setPreferentialTaxMark(goodsData.getPreferentialTaxMark().getMark());
        receiptApplyItem.setTaxRateMark(goodsData.getTaxRateMark().getMark());
        if (goodsData.getPreferentialTaxMark().getMark().equals(ReceiptEnum.PreferentialTaxMark.USED.getMark())) {
            receiptApplyItem.setSalesTaxManagement(goodsData.getSalesTaxManagement().getName());
        }
        receiptApplyItem.setNumber(1);
        receiptApplyItem.setIncludedTaxUnitPrice(includedTaxUnitPrice);
        return receiptApplyItem;
    }

    /**
     * 检查租金约束
     *
     * @param bill 账单
     * @return true:通过;false:不通过
     */
    private boolean checkRentConstraint(RealtyBillVo bill) {
        Realty realty = realtyService.findBySerial(bill.getRealtySerial());
        //非特殊物业的
        if (!rentAnywayRealties.contains(realty.getSerialNo())) {
            Contract realtyContract = contractService.findRealtyContract(realty.getId(), ContractType.ESTATE.getValue());
            return null == realtyContract;
        }
        return true;
    }

    /**
     * 检查抬头约束
     *
     * @param bill      账单
     * @param taxHeader 抬头
     * @return true:通过;false:不通过
     */
    public boolean checkHeaderConstraint(RealtyBillVo bill, TaxHeaderVo taxHeader) {
        Contract contract = contractService.findByContractNo(bill.getContractNo());
        return null == contract || Objects.equals(contract.getCustomerName(), taxHeader.getHeader());
    }

    /**
     * 添加物业发票
     *
     * @param receiptApplyDto   账单发票申请dto
     * @param receiptApplyList  物业发票集合
     * @param billReceiptManger 物业账单发票管理
     * @param taxHeader         发票抬头
     * @return
     */
    private RealtyReceipt saveRealtyReceipt(BillReceiptApplyDto receiptApplyDto, List<ReceiptApplyDto> receiptApplyList, RealtyReceiptMangerVo billReceiptManger, TaxHeaderVo taxHeader) {
        if (null == receiptApplyDto || CollectionUtils.isEmpty(receiptApplyDto.getBillMap())) {
            throw new BusinessException("未找到物业应付账单明细参数");
        }
        if (CollectionUtils.isEmpty(receiptApplyList)) {
            throw new BusinessException("未找到物业发票集合");
        }
        RealtyReceipt realtyReceipt = new RealtyReceipt();
        realtyReceipt.setTitle("物业发票");
        realtyReceipt.setHeaderCategory(taxHeader.getCategory());
        realtyReceipt.setApplyAmount(receiptApplyDto.getBillMap()
                .values()
                .stream()
                .map(b -> b.stream().map(RealtyBillItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)).reduce(BigDecimal.ZERO, BigDecimal::add));
        realtyReceipt.setApplyMan(billReceiptManger.getOpenid());
        realtyReceipt.setApplyUserName(billReceiptManger.getApplyUserName());
        realtyReceiptMapper.add(realtyReceipt);
        //物业发票绑定发票单据号
        realtyReceiptMapper.bindReceipt(realtyReceipt.getId(), receiptApplyList.stream().map(ReceiptApplyDto::getSerialNo).collect(Collectors.toList()));
        return realtyReceipt;
    }

    /**
     * 物业发票绑定物业账单
     *
     * @param receiptId     发票申请id
     * @param realtyBillIds 账单id列表
     */
    private void realtyReceiptBindRealtyBill(long receiptId, List<Long> realtyBillIds) {
        if (!WrapperClassUtils.biggerThanLong(receiptId, 0) || CollectionUtils.isEmpty(realtyBillIds)) {
            return;
        }
        realtyReceiptMapper.realtyReceiptBindRealtyBill(receiptId, realtyBillIds);
    }

    /**
     * 物业发票解绑物业账单
     *
     * @param receiptId     物业发票申请id
     * @param realtyBillIds 账单id列表
     */
    private void realtyReceiptUnbindRealtyBill(long receiptId, List<Long> realtyBillIds) {
        if (!WrapperClassUtils.biggerThanLong(receiptId, 0) || CollectionUtils.isEmpty(realtyBillIds)) {
            return;
        }
        realtyReceiptMapper.realtyReceiptUnbindRealtyBill(receiptId, realtyBillIds);
    }

    /**
     * 物业开票申请
     *
     * @param realtyReceiptManger 物业账单发票管理
     * @param rentConstraint      租金约束
     * @param headerConstraint    抬头约束
     * @param validityConstraint  有效期约束
     */
    @Transactional(rollbackFor = Exception.class)
    public void receiptApply(RealtyReceiptMangerVo realtyReceiptManger, boolean rentConstraint, boolean headerConstraint, boolean validityConstraint) {
        //获取发票抬头
        TaxHeaderVo taxHeader = receiptClient.getTaxHeader(realtyReceiptManger.getTaxHeaderId());
        Map<Long, List<Long>> billReceiptMap = new HashMap<>();
        for (RealtyReceiptMangerVo.BillReceipt billReceipt : realtyReceiptManger.getBillReceiptList()) {
            billReceiptMap.put(billReceipt.getBillId(), billReceipt.getFees());
        }
        //获取账单
        List<RealtyBillVo> realtyBillList = realtyBillService.listBillById(new ArrayList<>(billReceiptMap.keySet()), false);
        //获取物业税率
        Map<String, RealtyVo> realtyTaxRateMap = getRealtyRateBySerial(realtyBillList.stream().map(RealtyBillVo::getRealtySerial).collect(Collectors.toList()));
        //校验账单
        checkBill(realtyBillList, validityConstraint, true);
        //回写合同抬头信息
        contractService.updateBusinessLicenseById(taxHeader, realtyBillList);
        //获取账单需要开具的费项
        BillReceiptApplyDto receiptApplyDto = billItemApplyBuilder(realtyBillList, billReceiptMap, taxHeader, rentConstraint, headerConstraint);
        //发票申请
        List<ReceiptApplyDto> receiptApplyList = saveReceiptApply(receiptApplyDto, realtyReceiptManger, realtyTaxRateMap);
        //添加物业发票
        RealtyReceipt realtyReceipt = saveRealtyReceipt(receiptApplyDto, receiptApplyList, realtyReceiptManger, taxHeader);
        //有效的物业账单id
        List<Long> effectiveBillList = receiptApplyDto.getBillMap()
                .values().stream().flatMap(List::stream)
                .map(RealtyBillItem::getBillId).distinct().collect(Collectors.toList());
        //物业发票绑定到物业账单
        realtyReceiptBindRealtyBill(realtyReceipt.getId(), effectiveBillList);
        //是否自动审核
        if (BooleanUtils.isFalse(receiptConfig.getArtificialAudit())) {
            audit(realtyReceipt.getId(), ReceiptStatus.AUDIT_APPROVED, "自动审核", 1L);
        }
    }

    /**
     * 根据物业编号获取物业税率
     *
     * @param serialNos 物业编号集
     * @return 返回获取到的物业
     */
    private Map<String, RealtyVo> getRealtyRateBySerial(List<String> serialNos) {
        if (CollectionUtils.isEmpty(serialNos)) {
            return Collections.emptyMap();
        }
        RealtySearchVo search = new RealtySearchVo();
        search.setPage(false);
        search.setSerialNos(serialNos);
        List<RealtyVo> realtyList = realtyService.listRealtyTaxRate(search);
        return realtyList.stream().collect(Collectors.toMap(RealtyVo::getSerialNo, Function.identity()));
    }

    /**
     * 构建账单费项申请
     *
     * @param realtyBillList   物业账单列表
     * @param billReceiptMap   账单申请map
     * @param taxHeader        发票抬头
     * @param rentConstraint   租金约束
     * @param headerConstraint 抬头约束
     * @return 账单费项申请
     */
    private BillReceiptApplyDto billItemApplyBuilder(List<RealtyBillVo> realtyBillList, Map<Long, List<Long>> billReceiptMap, TaxHeaderVo taxHeader, boolean rentConstraint, boolean headerConstraint) {
        if (CollectionUtils.isEmpty(realtyBillList)) {
            return null;
        }
        BillReceiptApplyDto applyDto = new BillReceiptApplyDto();
        Map<RealtyBillVo, List<RealtyBillItem>> billMap = new HashMap<>(realtyBillList.size());
        List<Long> billIds = realtyBillList.stream().map(RealtyBillVo::getId).collect(Collectors.toList());
        Map<Long, List<RealtyBillItem>> billItemMap = billMapper.listBillItemByBillIds(billIds)
                .stream()
                .collect(Collectors.groupingBy(RealtyBillItem::getBillId));
        realtyBillList.forEach(bill -> {
            //获取账单费项
            List<RealtyBillItem> billItems = billItemMap.get(bill.getId());
            if (CollectionUtils.isEmpty(billItems)) {
                throw new BusinessException(String.format("[%s]账单没有费用项", bill.getId()));
            }
            List<Long> fees = billReceiptMap.get(bill.getId());
            List<RealtyBillItem> validItems = new ArrayList<>(billItems.size());
            billItems.forEach(billItem -> {
                if (!checkBillItemReceiptCondition(bill, billItem, fees, taxHeader, rentConstraint, headerConstraint)) {
                    return;
                }
                validItems.add(billItem);
            });
            if (CollectionUtils.isEmpty(validItems)) {
                return;
            }
            billMap.put(bill, validItems);
        });
        applyDto.setBillMap(billMap);
        if (billMap.isEmpty()) {
            throw new BusinessException("没有可开具的费项。如有疑问，请到客户中心处理！");
        }
        List<Long> times = billMap.keySet().stream().map(b -> LocalDate.of(b.getBillYear(), b.getBillMonth(), 1).atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli()).collect(Collectors.toList());
        applyDto.setMinTimeLong(times.stream().min(Long::compareTo).orElseThrow(() -> new RuntimeException("Time min error.")));
        applyDto.setMaxTimeLong(times.stream().max(Long::compareTo).orElseThrow(() -> new RuntimeException("Time max error.")));
        applyDto.setTaxHeader(taxHeader);
        return applyDto;
    }

    /**
     * 物业账单发票重置
     *
     * @param realtyReceiptId     物业发票id
     * @param receiptSerialNoList 发票单据编号列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void realtyBillReceiptReset(Long realtyReceiptId, List<String> receiptSerialNoList) {
        if (!WrapperClassUtils.biggerThanLong(realtyReceiptId, 0L)
                || CollectionUtils.isEmpty(receiptSerialNoList)
        ) {
            return;
        }
        List<RealtyBillItem> billItemList = billMapper.listBillItemByReceiptSerial(receiptSerialNoList);
        //恢复开具状态
        billItemList.forEach(item -> {
            item.setReceiptSerialNo("");
            item.setReceiptStatus(ReceiptStatus.PENDING_ISSUE.getStatus());
        });
        //更新账单费项
        realtyBillService.batchUpdateBillItemReceipt(billItemList);
        //物业发票申请取消绑定物业账单
        realtyReceiptUnbindRealtyBill(realtyReceiptId, billItemList.stream().map(RealtyBillItem::getBillId).distinct().collect(Collectors.toList()));
    }

    /**
     * 检查账单发票费项条件
     *
     * @param bill             账单
     * @param item             账单费项
     * @param fees             项
     * @param taxHeader        抬头
     * @param rentConstraint   租金约束
     * @param headerConstraint 抬头约束
     * @return true:满足;false:不满足
     */
    private boolean checkBillItemReceiptCondition(RealtyBillVo bill
            , RealtyBillItem item
            , List<Long> fees
            , TaxHeaderVo taxHeader
            , boolean rentConstraint
            , boolean headerConstraint) {
        //排除自定义开票项的
        if (!CollectionUtils.isEmpty(fees) && !fees.contains(item.getFeeId())) {
            return false;
        }
        //租金
        if (item.getFeeId().equals((long) RealtyFee.RENT.getFeeId())) {
            if (rentConstraint && !checkRentConstraint(bill)) {
                log.info("【Receipt】排除[{}]有物业合同的租金项: {}", bill.getId(), JsonUtils.object2Json(item));
                return false;
            }
            if (headerConstraint && !checkHeaderConstraint(bill, taxHeader)) {
                log.info("【Receipt】排除[{}]客户名称跟营业执照名称不一致的租金项: {}", bill.getId(), JsonUtils.object2Json(item));
                return false;
            }
        }
        if (!item.getReceiptStatus().equals(ReceiptStatus.PENDING_ISSUE.getStatus())
                || item.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("【Receipt】排除[{}]非待开具、费用为0的: {}", bill.getId(), JsonUtils.object2Json(item));
            return false;
        }
        return true;
    }


}

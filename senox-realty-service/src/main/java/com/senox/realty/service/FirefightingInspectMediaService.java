package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.domain.FirefightingInspectMedia;
import com.senox.realty.mapper.FirefightingInspectMediaMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/6 10:56
 */
@Service
public class FirefightingInspectMediaService extends ServiceImpl<FirefightingInspectMediaMapper, FirefightingInspectMedia> {

    /**
     * 保存多媒体资料
     * @param inspectId
     * @param inspectType
     * @param medias
     */
    public void saveMedias(Long inspectId, InspectionType inspectType, List<String> medias) {
        saveMedias(inspectId, inspectType, 1, medias);
    }

    /**
     * 保存多媒体资料
     * @param inspectId
     * @param inspectType
     * @param inspectTimes
     * @param medias
     */
    public void saveMedias(Long inspectId, InspectionType inspectType, Integer inspectTimes, List<String> medias) {
        if (medias == null) {
            return;
        }

        // 原数据
        List<FirefightingInspectMedia> srcList = findByInspectId(inspectId, inspectType);
        // 目标数据
        List<FirefightingInspectMedia> targetList = buildInspectMedias(inspectId, inspectType, inspectTimes, medias);
        // 数据比较
        DataSepDto<FirefightingInspectMedia> sepDto = separateData(srcList, targetList);

        if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
            saveBatch(sepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getRemoveList())) {
            List<Long> removeIds = sepDto.getRemoveList().stream()
                    .map(FirefightingInspectMedia::getId)
                    .collect(Collectors.toList());
            removeByIds(removeIds);
        }
    }



    /**
     * 根据巡检id删除多媒体信息
     * @param inspectId
     * @param inspectType
     */
    public void deleteByInspectId(Long inspectId, InspectionType inspectType) {
        if (!WrapperClassUtils.biggerThanLong(inspectId, 0L) || inspectType == null) {
            return;
        }
        Wrapper<FirefightingInspectMedia> queryWrapper = new QueryWrapper<FirefightingInspectMedia>()
                .lambda()
                .eq(FirefightingInspectMedia::getInspectId, inspectId)
                .eq(FirefightingInspectMedia::getInspectType, inspectType);
        remove(queryWrapper);
    }

    /**
     * 根据巡检id查找多媒体信息
     * @param inspectId
     * @param inspectType
     * @return
     */
    public List<FirefightingInspectMedia> findByInspectId(Long inspectId, InspectionType inspectType) {
        if (!WrapperClassUtils.biggerThanLong(inspectId, 0L) || inspectType == null) {
            return Collections.emptyList();
        }

        Wrapper<FirefightingInspectMedia> queryWrapper = new QueryWrapper<FirefightingInspectMedia>()
                .lambda()
                .eq(FirefightingInspectMedia::getInspectId, inspectId)
                .eq(FirefightingInspectMedia::getInspectType, inspectType);
        return list(queryWrapper);
    }

    /**
     * 巡检多媒体
     * @param inspectId
     * @param inspectType
     * @param inspectTimes
     * @param medias
     * @return
     */
    private List<FirefightingInspectMedia> buildInspectMedias(Long inspectId,
                                                              InspectionType inspectType,
                                                              Integer inspectTimes,
                                                              List<String> medias) {
        if (CollectionUtils.isEmpty(medias)) {
            return Collections.emptyList();
        }

        List<FirefightingInspectMedia> resultList = new ArrayList<>(medias.size());
        for (String item : medias) {
            FirefightingInspectMedia media = new FirefightingInspectMedia(inspectId, inspectType, inspectTimes, item);
            media.setModifiedTime(LocalDateTime.now());
            resultList.add(media);
        }
        return resultList;
    }

    /**
     * 数据比较
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<FirefightingInspectMedia> separateData(List<FirefightingInspectMedia> srcList,
                                                              List<FirefightingInspectMedia> targetList) {
        DataSepDto<FirefightingInspectMedia> result = new DataSepDto<>();

        if (CollectionUtils.isEmpty(srcList)) {
            result.setAddList(targetList);

        } else if (CollectionUtils.isEmpty(targetList)) {
            result.setRemoveList(targetList);

        } else {
            List<FirefightingInspectMedia> addList = targetList.stream()
                    .filter(x -> !srcList.contains(x))
                    .collect(Collectors.toList());

            List<FirefightingInspectMedia> removeList = srcList.stream()
                    .filter(x -> !targetList.contains(x))
                    .collect(Collectors.toList());

            result.setAddList(addList);
            result.setRemoveList(removeList);
        }


        return result;
    }



}

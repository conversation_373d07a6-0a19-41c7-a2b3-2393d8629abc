package com.senox.realty.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.OrderType;
import com.senox.pm.vo.OrderCancelVo;
import com.senox.pm.vo.OrderProductDetailVo;
import com.senox.realty.component.CustomerComponent;
import com.senox.realty.component.OrderComponent;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.Contract;
import com.senox.realty.domain.Fee;
import com.senox.realty.domain.RealtyDeposit;
import com.senox.realty.mapper.RealtyDepositMapper;
import com.senox.realty.vo.BillTollVo;
import com.senox.realty.vo.RealtyDepositSearchVo;
import com.senox.realty.vo.RealtyDepositVo;
import com.senox.realty.vo.TollSerialVo;
import com.senox.user.vo.CustomerVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.senox.realty.constant.RealtyConst.BATCH_SIZE_1000;

/**
 * <AUTHOR>
 * @date 2021/11/3 10:54
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RealtyDepositService {

    private final Fee depositFee;
    private final ContractService contractService;
    private final RealtyDepositMapper depositMapper;
    private final CustomerComponent customerComponent;
    private final OrderComponent orderComponent;


    /**
     * 添加物业押金
     * @param deposit
     * @return
     */
    public Long addDeposit(RealtyDeposit deposit) {
        checkContractDeposit(deposit.getContractId());

        prepareDeposit(deposit);
        // 合同信息初始化
        prepareContractDeposit(deposit);
        prepareDepositCustomer(deposit);

        int result = depositMapper.addDeposit(deposit);
        return result < 1 ? 0L : deposit.getId();
    }

    /**
     * 批量保存物业押金
     * @param depositList
     */
    public void batchAddDeposit(List<RealtyDeposit> depositList) {
        // 押金清单过滤
        depositList = filterDeposits(depositList);
        if (CollectionUtils.isEmpty(depositList)) {
            return;
        }

        // 押金清单校验
        checkDeposits(depositList);

        // 押金清单初始化
        depositList.forEach(x -> {
            prepareDeposit(x);
            prepareContractDeposit(x);
            prepareDepositCustomer(x);
        });

        for (int offset = 0; offset < depositList.size(); offset += BATCH_SIZE_1000) {
            List<RealtyDeposit> list = depositList.stream().skip(offset).limit(BATCH_SIZE_1000).collect(Collectors.toList());
            depositMapper.batchAddDeposit(list);
        }
    }

    /**
     * 更新物业押金
     * @param deposit
     */
    public void updateDeposit(RealtyDeposit deposit) {
        if (!WrapperClassUtils.biggerThanLong(deposit.getId(), 0L)) {
            return;
        }
        RealtyDepositVo dbDeposit = findById(deposit.getId());
        BillStatus billStatus = BillStatus.fromStatus(dbDeposit.getStatus());
        if (billStatus != BillStatus.INIT) {
            throw new BusinessException("变更失败，订单已缴费。");
        }
        // 合同信息初始化
        prepareContractDeposit(deposit);
        prepareDepositCustomer(deposit);
        depositMapper.updateDeposit(deposit);
    }

    /**
     * 更新押金账单状态
     *
     * @param billPaid
     */
    public void updateOneTimeFeeBillStatus(BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            return;
        }
        if (!CollectionUtils.isEmpty(billPaid.getBillIds())) {
            log.info("根据账单id更新押金账单状态-{}", JsonUtils.object2Json(billPaid));
            updateDepositPaidById(billPaid);

        } else {
            log.info("根据支付订单id更新押金账单状态-{}", JsonUtils.object2Json(billPaid));
            updateDepositPaidByRemoteOrder(billPaid);
        }
    }

    /**
     * 更新押金票据号
     * @param serial
     */
    public void updateDepositTollSerial(TollSerialVo serial) {
        if (!WrapperClassUtils.biggerThanLong(serial.getId(), 0L)) {
            return;
        }

        // 押金单
        RealtyDepositVo deposit = findById(serial.getId());
        if (deposit == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "找不到物业押金");
        }
        BillStatus billStatus = BillStatus.fromStatus(deposit.getStatus());
        if (BooleanUtils.isTrue(serial.getRefund())) {
            if (billStatus != BillStatus.REFUND) {
                throw new BusinessException("押金未退档");
            }
        } else {
            if (billStatus == null || billStatus == BillStatus.INIT) {
                throw new BusinessException("押金未缴费");
            }
        }
        depositMapper.updateDepositTollSerial(serial);
    }



    /**
     * 支付物业押金
     * @param id
     * @param toll
     */
    public void payDeposit(Long id, BillTollVo toll) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        // 押金单
        RealtyDepositVo deposit = findById(id);
        if (deposit != null && deposit.getStatus() != BillStatus.INIT.getStatus()) {
            throw new BusinessException("押金已支付");
        }
        if (StringUtils.isBlank(toll.getSerial())) {
            toll.setSerial(StringUtils.EMPTY);
        }
        toll.setId(id);
        toll.setStatus(BillStatus.PAID.getStatus());
        depositMapper.payDeposit(toll);
    }

    /**
     * 撤销物业押金支付
     * @param billToll
     */
    @Transactional(rollbackFor = Exception.class)
    public void revokeDepositPayment(BillTollVo billToll) {
        if (!WrapperClassUtils.biggerThanLong(billToll.getId(), 0L)) {
            return;
        }

        // 押金单
        RealtyDepositVo deposit = findById(billToll.getId());
        if (deposit != null && deposit.getStatus() != BillStatus.PAID.getStatus()) {
            throw new BusinessException("押金未支付或已退档");
        }

        // 撤销支付
        depositMapper.revokeDepositPaid(billToll);


        // 取消 p_order 表支付
        if (deposit != null && WrapperClassUtils.biggerThanLong(deposit.getRemoteOrderId(), 0L)) {
            List<OrderProductDetailVo> productOrderList = orderComponent.listProductByProductId(OrderType.DEPOSIT, deposit.getId());
            productOrderList = productOrderList.stream().filter(x -> DecimalUtils.isPositive(x.getAmount())).collect(Collectors.toList());

            for (OrderProductDetailVo item : productOrderList) {
                OrderCancelVo cancel = new OrderCancelVo();
                cancel.setOrderId(item.getOrderId());
                cancel.setProductIds(Collections.singletonList(item.getProductId()));
                cancel.setRemark(RealtyConst.REMARK_DEPOSIT_PAY_CANCEL);
                orderComponent.cancelOrder(cancel);
            }
        }
    }

    /**
     * 物业押金退费
     * @param id
     * @param toll
     */
    public void refundDeposit(Long id, BillTollVo toll) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        // 押金单
        RealtyDepositVo deposit = findById(id);
        if (deposit != null && deposit.getStatus() != BillStatus.PAID.getStatus()) {
            throw new BusinessException("押金未支付或已退档");
        }

        if (StringUtils.isBlank(toll.getSerial())) {
            toll.setSerial(StringUtils.EMPTY);
        }
        toll.setId(id);
        toll.setStatus(BillStatus.REFUND.getStatus());
        depositMapper.refundDeposit(toll);
    }

    /**
     * 撤销物业押金退费
     * @param billToll
     */
    public void revokeDepositRefund(BillTollVo billToll) {
        if (!WrapperClassUtils.biggerThanLong(billToll.getId(), 0L)) {
            return;
        }

        // 押金单
        RealtyDepositVo deposit = findById(billToll.getId());
        if (deposit != null && deposit.getStatus() != BillStatus.REFUND.getStatus()) {
            throw new BusinessException("押金未退档");
        }

        // 撤销退费
        depositMapper.revokeDepositRefund(billToll);

        // 取消 p_order 表退费订单
        if (deposit != null && WrapperClassUtils.biggerThanLong(deposit.getRefundOrderId(), 0L)) {
            List<OrderProductDetailVo> productOrderList = orderComponent.listProductByProductId(OrderType.DEPOSIT, billToll.getId());
            productOrderList = productOrderList.stream().filter(x -> DecimalUtils.isNegative(x.getAmount())).collect(Collectors.toList());

            for (OrderProductDetailVo item : productOrderList) {
                OrderCancelVo cancel = new OrderCancelVo();
                cancel.setOrderId(item.getOrderId());
                cancel.setProductIds(Collections.singletonList(item.getProductId()));
                cancel.setRemark(RealtyConst.REMARK_DEPOSIT_REFUND_CANCEL);
                orderComponent.cancelOrder(cancel);
            }
        }
    }

    /**
     * 根据id查找物业押金
     * @param id
     * @return
     */
    public RealtyDepositVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        return depositMapper.findById(id);
    }

    /**
     * 根据id列表查找物业押金
     * @param ids
     * @return
     */
    public List<RealtyDepositVo> listByIds(List<Long> ids) {
        return CollectionUtils.isEmpty(ids) ? Collections.emptyList() : depositMapper.listByIds(ids);
    }

    /**
     * 合同押金
     * @param contractId
     * @return
     */
    public List<RealtyDepositVo> listContractDeposit(Long contractId) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L)) {
            return Collections.emptyList();
        }

        return depositMapper.listContractDeposit(contractId);
    }

    /**
     * 物业押金合计
     * @param search
     * @return
     */
    public RealtyDepositVo sumDeposit(RealtyDepositSearchVo search) {
        return depositMapper.sumDeposit(search);
    }

    /**
     * 物业押金记录列表
     * @param search
     * @return
     */
    public PageResult<RealtyDepositVo> listDepositPage(RealtyDepositSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = depositMapper.countDeposit(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<RealtyDepositVo> resultList = depositMapper.listDeposit(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 物业押金初始化
     * @param deposit
     */
    private void prepareDeposit(RealtyDeposit deposit) {
        if (deposit.getContractId() == null) {
            deposit.setContractId(0L);
        }
        if (deposit.getCustomerId() == null) {
            deposit.setCustomerId(0L);
        }
        if (deposit.getRealtyId() == null) {
            deposit.setRealtyId(0L);
        }
        if (deposit.getAmount() == null) {
            deposit.setAmount(BigDecimal.ZERO);
        }
        if (deposit.getFeeId() == null) {
            deposit.setFeeId(depositFee.getId());
            deposit.setFeeName(depositFee.getName());
        }
        if (deposit.getOperateDate() == null) {
            deposit.setOperateDate(LocalDate.now());
        }
    }

    /**
     * 根据合同初始化物业押金
     * @param deposit
     */
    private void prepareContractDeposit(RealtyDeposit deposit) {
        // 合同信息初始化
        if (!WrapperClassUtils.biggerThanLong(deposit.getContractId(), 0L)) {
            return;
        }

        Contract contract = contractService.findById(deposit.getContractId());
        if (contract != null) {
            deposit.setRealtyId(contract.getRealtyId() == null ? 0L : contract.getRealtyId());
            deposit.setCustomerId(contract.getCustomerId() == null ? 0L : contract.getCustomerId());
        }
    }

    /**
     * 客户姓名
     * @param deposit
     */
    private void prepareDepositCustomer(RealtyDeposit deposit) {
        if (WrapperClassUtils.biggerThanLong(deposit.getCustomerId(), 0L) && StringUtils.isBlank(deposit.getCustomerName())) {
            CustomerVo customer = customerComponent.findById(deposit.getCustomerId());
            if (customer != null) {
                deposit.setCustomerName(customer.getName());
            }
        }
    }

    private List<RealtyDeposit> filterDeposits(List<RealtyDeposit> depositList) {
        if (CollectionUtils.isEmpty(depositList)) {
            return Collections.emptyList();
        }

        return depositList.stream().filter(x -> DecimalUtils.isPositive(x.getAmount())).collect(Collectors.toList());
    }

    private void checkDeposits(List<RealtyDeposit> depositList) {
        if (CollectionUtils.isEmpty(depositList)) {
            return;
        }

        if (depositList.stream().map(RealtyDeposit::getContractId).distinct().count() > 1) {
            throw new BusinessException("一次性只允许添加一个物业的合同");
        }

        // 一个合同只允许添加一次押金
        checkContractDeposit(depositList.get(0).getContractId());
    }

    private void checkContractDeposit(Long contractId) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L)) {
            return;
        }

        if (!CollectionUtils.isEmpty(listContractDeposit(contractId))) {
            throw new BusinessException("合同已添加押金");
        }
    }

    /**
     * 根据押金单id更新押金状态
     *
     * @param billPaid
     * @return
     */
    private void updateDepositPaidById(BillPaidVo billPaid) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds())) {
            return;
        }

        if (BooleanUtils.isTrue(billPaid.getRefund())) {
            depositMapper.updateDepositRefundById(billPaid);
        } else {
            depositMapper.updateDepositPaidById(billPaid);
        }
    }

    /**
     * 根据支付订单id更新押金状态
     *
     * @param billPaid
     * @return
     */
    private void updateDepositPaidByRemoteOrder(BillPaidVo billPaid) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds())) {
            return;
        }

        if (BooleanUtils.isTrue(billPaid.getRefund())) {
            depositMapper.updateDepositRefundByRemoteOrder(billPaid);
        } else {
            depositMapper.updateDepositPaidByRemoteOrder(billPaid);
        }
    }
}

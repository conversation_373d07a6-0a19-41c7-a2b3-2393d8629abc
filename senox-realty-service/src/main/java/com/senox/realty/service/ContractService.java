package com.senox.realty.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.vo.TaxHeaderVo;
import com.senox.realty.constant.ContractStatus;
import com.senox.realty.constant.ContractType;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.*;
import com.senox.realty.mapper.ContractExtMapper;
import com.senox.realty.mapper.ContractFeeMapper;
import com.senox.realty.mapper.ContractMapper;
import com.senox.realty.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/9 9:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractService {

    @Value("${senox.contract.serial.length}")
    private Integer contractNoPostfixLength;
    @Value("${senox.contract.serial.fillChar}")
    private Character fillChar;
    @Value("#{'${senox.realty.rent-anyway:}'.split(',')}")
    private List<String> rentAnywayRealties;

    private final ContractMapper contractMapper;
    private final ContractExtMapper contractExtMapper;
    private final ContractFeeMapper contractFeeMapper;
    private final RealtyService realtyService;


    /**
     * 新建合同
     * @param contract
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addContract(Contract contract, ContractExt ext, List<ContractFee> fees) {
        // 添加合同
        boolean result = addContract(contract);

        if (result) {
            // 合同扩展信息
            prepareContractExt(contract, ext);
            addContractExt(ext);

            if (!CollectionUtils.isEmpty(fees)) {
                for (ContractFee fee : fees) {
                    fee.setContractId(contract.getId());
                    fee.setCreatorId(contract.getCreatorId());
                    fee.setCreatorName(contract.getCreatorName());
                    fee.setModifierId(contract.getModifierId());
                    fee.setModifierName(contract.getModifierName());
                    if (fee.getPeriod() == null) {
                        fee.setPeriod(1);
                    }
                    if (fee.getRentFreePeriod() == null) {
                        fee.setRentFreePeriod(0);
                    }
                }
                batchAddContractFee(fees);
            }
        }
        return result ? contract.getId() : 0L;
    }

    /**
     * 更新合同
     * @param contract
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContract(Contract contract, ContractExt ext, List<ContractFee> fees) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            return false;
        }
        // 判断是否停用合同,若是停用合同，更新stopBy, stopTime属性
        ContractStatus contractStatus = ContractStatus.fromValue(contract.getStatus());
        if (contractStatus == ContractStatus.SUSPEND) {
            Contract c = findById(contract.getId());
            if (!Objects.equals(c.getStatus(), contract.getStatus())) {
                contract.setStopBy(c.getModifierId());
                contract.setStopTime(LocalDateTime.now());
            }
        }

        boolean result = updateContract(contract);
        if (result) {
            saveContractExtCascade(contract, ext);

            // 费项
            saveContractFeeCascade(contract, fees);
        }
        return result;
    }

    public boolean addContract(Contract contract) {
        return addContract(contract, false);
    }

    /**
     * 新增合同
     * @param contract
     * @param isSync
     * @return
     */
    public boolean addContract(Contract contract, boolean isSync) {
        // 合同合法性校验
        if (!isSync) {
            checkContract(contract);
        }
        // 默认参数初始化
        prepareContract(contract);
        return contractMapper.addContract(contract) > 0;
    }

    /**
     * 更新合同
     * @param contract
     * @return
     */
    public boolean updateContract(Contract contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getId(), 0L)) {
            return false;
        }

        // 合同合法性校验
        if (!BooleanUtils.isTrue(contract.getDisabled())) {
            checkContract(contract);
        }
        return contractMapper.updateContract(contract) > 0;
    }

    /**
     * 添加合同扩展信息
     * @param ext
     * @return
     */
    public boolean addContractExt(ContractExt ext) {
        boolean result = contractExtMapper.addContractExt(ext) > 0;
        if (result) {
            // 同步冗余更新物业水电单价
            realtyService.updateEnergyPriceByContract(ext);
        }
        return result;
    }

    /**
     * 级联更新合同扩展信息
     * @param contract
     * @param ext
     */
    private void saveContractExtCascade(Contract contract, ContractExt ext) {
        // 扩展信息
        if (ext == null) {
            return;
        }

        ext.setContractId(contract.getId());
        ext.setModifierId(contract.getModifierId());
        ext.setModifierName(contract.getModifierName());
        boolean result = contractExtMapper.updateContractExt(ext) > 0;

        if (result) {
            // 同步冗余更新物业水电单价
            realtyService.updateEnergyPriceByContract(ext);
        }
    }

    /**
     * 级联更新合同费项信息
     * @param contract
     * @param fees
     */
    public void saveContractFeeCascade(Contract contract, List<ContractFee> fees) {
        if (CollectionUtils.isEmpty(fees)) {
            return;
        }

        for(ContractFee fee : fees) {
            fee.setContractId(contract.getId());
            fee.setCreatorId(contract.getModifierId());
            fee.setCreatorName(contract.getModifierName());
            fee.setModifierId(contract.getModifierId());
            fee.setModifierName(contract.getModifierName());
            if (fee.getPeriod() == null) {
                fee.setPeriod(1);
            }
            if (fee.getRentFreePeriod() == null) {
                fee.setRentFreePeriod(0);
            }
        }

        // 与原记录比较
        List<ContractFee> srcList = listContractFee(contract.getId());
        DataSepDto<ContractFee> sepData = sepContractFee(srcList, fees);

        // 保存合同费项信息
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            batchAddContractFee(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            List<Long> ids = sepData.getRemoveList().stream().map(ContractFee::getId).collect(Collectors.toList());
            batchDeleteContractFee(contract.getId(), ids);
        }
    }

    /**
     * 批量添加费项
     * @param fees
     */
    public void batchAddContractFee(List<ContractFee> fees) {
        if (CollectionUtils.isEmpty(fees)) {
            return;
        }
        contractFeeMapper.batchAddContractFee(fees);
    }

    /**
     * 批量删除费项
     * @param contractId
     * @param feeIds
     */
    public void batchDeleteContractFee(Long contractId, List<Long> feeIds) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L)
                || CollectionUtils.isEmpty(feeIds)) {
            return;
        }
        contractFeeMapper.batchDeleteContractFee(contractId, feeIds);
    }

    /**
     * 启用合同
     * @param enableDto
     * @return
     */
    public int enableContract(ContractEnableDto enableDto) {
        if (StringUtils.isBlank(enableDto.getContractNo())) {
            log.warn("【合同启用】合同启用失败，参数不明确");
            return 0;
        }

        return contractMapper.enableContract(enableDto);
    }

    /**
     * 通过合同号关闭合同
     * @param suspendDto
     * @return
     */
    public int suspendContract(RealtyContractSuspendDto suspendDto) {
        // 根据合同号停用合同
        if (!StringUtils.isBlank(suspendDto.getContractNo())) {
            log.info("【合同停用】根据合同号停用合同 {}", suspendDto.getContractNo());
            return contractMapper.suspendByContractNo(suspendDto);
        }

        // 根据物业id停用合同
        if (WrapperClassUtils.biggerThanLong(suspendDto.getRealtyId(), 0L)
                && ContractType.fromValue(suspendDto.getContractType()) != null) {
            log.info("【合同停用】根据物业和合同类型停用合同，物业id {}，合同类型 {} 合同开始时间 {}。", suspendDto.getRealtyId(), suspendDto.getContractType(), suspendDto.getContractStartDate());
            return contractMapper.suspendByRealtyAndType(suspendDto);
        }

        log.warn("【合同停用】合同停用失败，参数不明确");
        return 0;
    }

    /**
     * 根据id查找合同
     * @param id
     * @return
     */
    public Contract findById(Long id) {
        return !WrapperClassUtils.biggerThanLong(id, 0L) ? null : contractMapper.findById(id);
    }

    /**
     * 根据合同号查找合同
     * @param contractNo
     * @return
     */
    public Contract findByContractNo(String contractNo) {
        return StringUtils.isBlank(contractNo) ? null : contractMapper.findByContractNo(contractNo);
    }

    /**
     * 根据合同号批量获取合同
     *
     * @param contractNos 合同号列表
     * @return 合同列表
     */
    public List<Contract> listByContractNos(List<String> contractNos) {
        return CollectionUtils.isEmpty(contractNos) ? Collections.emptyList() : contractMapper.listByContractNos(contractNos);
    }

    /**
     * 物业最新合同
     * @param realtyId
     * @return
     */
    public List<Contract> listRealtyAvailableContract(Long realtyId) {
        return WrapperClassUtils.biggerThanLong(realtyId, 0L)
                ? contractMapper.listAvailableContractByRealtyId(realtyId, LocalDate.now()) : Collections.emptyList();
    }

    /**
     * 查找物业离查询日期最近的租赁合同
     * @param realtyId
     * @param date
     * @return
     */
    public ContractVo findRealtyLatestLeaseContract(Long realtyId, LocalDate date) {
        if (!WrapperClassUtils.biggerThanLong(realtyId, 0L)) {
            return null;
        }

        ContractSearchVo searchVo = new ContractSearchVo();
        // 取最近的一条
        searchVo.setPageNo(1);
        searchVo.setPageSize(1);
        searchVo.setOrderStr("id DESC");
        // 租赁合同
        searchVo.setType(ContractType.LEASE.getValue());

        // 物业
        searchVo.setRealtyId(realtyId);
        searchVo.setStatus(ContractStatus.EFFECTIVE.ordinal());
        // 账单日期在这个日期期间
        searchVo.setStartDateEnd(date);
        searchVo.setEndDateBegin(date);

        searchVo.prepare();
        List<ContractVo> list = contractMapper.listContract(searchVo);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 根据id获取合同及物业、客户信息
     * @param id
     * @return
     */
    public ContractVo findWithRealtyAndCustomerById(Long id) {
        return !WrapperClassUtils.biggerThanLong(id, 0L) ? null : contractMapper.findWithRealtyAndCustomerById(id);
    }

    /**
     * 查续签源
     * @return
     */
    public ContractVo findRenewFrom(Long id) {
        return !WrapperClassUtils.biggerThanLong(id, 0L) ? null : contractMapper.findRenewFrom(id);
    }

    /**
     * 查找合同及合同信息
     * @param contractNo 合同号
     * @return
     */
    public ContractVo findWithExtByContractNo(String contractNo) {
        return StringUtils.isBlank(contractNo) ? null : contractMapper.findWithExtByContractNo(contractNo);
    }

    /**
     * 根据合同id查找合同扩展信息
     * @param contractId
     * @return
     */
    public ContractExt findExtByContractId(Long contractId) {
        return !WrapperClassUtils.biggerThanLong(contractId, 0L)
                ? null : contractExtMapper.findExtByContractId(contractId);
    }

    /**
     * 合同费项列表
     * @param contractId
     * @return
     */
    public List<ContractFee> listContractFee(Long contractId) {
        return !WrapperClassUtils.biggerThanLong(contractId, 0L)
                ? Collections.emptyList() : contractFeeMapper.listContractFee(contractId);
    }

    /**
     * 合同某费项列表
     * @param contractId
     * @param feeId
     * @return
     */
    public List<ContractFee> listContractFeeByFeeId(Long contractId, Integer feeId) {
        if (!WrapperClassUtils.biggerThanLong(contractId, 0L)
                || !WrapperClassUtils.biggerThanInt(feeId, 0)) {
            return Collections.emptyList();
        }

        return contractFeeMapper.listContractFeeByFeeId(contractId, feeId);
    }


    /**
     * 合同列表
     * @param search
     * @return
     */
    public List<ContractVo> listBillContract(ContractBillSearchVo search) {
        return contractMapper.listBillContract(search);
    }

    /**
     * 统计合同
     * @param search
     * @return
     */
    public int countContract(ContractSearchVo search) {
        return contractMapper.countContract(search);
    }

    /**
     * 合同列表
     * @param search
     * @return
     */
    public List<ContractVo> listContract(ContractSearchVo search) {
        return contractMapper.listContract(search);
    }

    /**
     * 根据物业号查找指定类型的合同
     * @param realtySerial
     * @param contractTypes
     * @return
     */
    public List<ContractVo> listEffectiveContractByRealtySerial(String realtySerial, List<Integer> contractTypes) {
        if (StringUtils.isBlank(realtySerial)) {
            return Collections.emptyList();
        }

        ContractSearchVo search = new ContractSearchVo();
        search.setPage(false);
        search.setStatus(ContractStatus.EFFECTIVE.ordinal());
        search.setRealtySerial(realtySerial);
        search.setTypes(contractTypes);
        return listContract(search);
    }

    /**
     * 合同分页
     * @param search
     * @return
     */
    public PageResult<ContractVo> listContractPage(ContractSearchVo search) {
       return  PageUtils.commonPageResult(search, () -> countContract(search), () -> listContract(search));
    }

    /**
     * 租赁合同合计
     * @param search
     * @return
     */
    public int countLeaseContract(ContractSearchVo search) {
        return contractMapper.countLeaseContract(search);
    }

    /**
     * 租赁合同列表
     * @param search
     * @return
     */
    public List<LeaseContractListVo> listLeaseContract(ContractSearchVo search) {
        return contractMapper.listLeaseContract(search);
    }

    /**
     * 合同分页
     * @param search
     * @return
     */
    public PageResult<LeaseContractListVo> listLeaseContractPage(ContractSearchVo search) {
        return  PageUtils.commonPageResult(search, () -> countLeaseContract(search), () -> listLeaseContract(search));
    }

    /**
     * 合同银行信息列表
     * @param search
     * @return
     */
    public PageResult<ContractBankVo> listContractBank(ContractSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = countContract(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<ContractBankVo> resultList = contractMapper.listContractBank(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 按物业、合同类型查找合同
     * @param realtyId
     * @param contractType
     * @return
     */
    public Contract findRealtyContract(Long realtyId, Integer contractType) {
        return findRealtyContract(null, realtyId, contractType);
    }

    private Contract findRealtyContract(Long contractId, Long realtyId, Integer contractType) {
        return contractMapper.findRealtyLastContract(contractId, realtyId, contractType);
    }

    /**
     * 更新合同营业执照信息
     * @param taxHeader 发票抬头
     * @param realtyBillList 物业账单列表
     */
    public void updateBusinessLicenseById(TaxHeaderVo taxHeader, List<RealtyBillVo> realtyBillList) {
        if (null == taxHeader || CollectionUtils.isEmpty(realtyBillList)) {
            return;
        }
        List<Contract> contractList = listByContractNos(realtyBillList.stream().map(RealtyBillVo::getContractNo).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(contractList)) {
            return;
        }
        Map<String, Contract> contractMap = contractList.stream().collect(Collectors.toMap(Contract::getContractNo, Function.identity()));
        for (RealtyBillVo bill : realtyBillList) {
            Contract contract = contractMap.get(bill.getContractNo());
            if (null == contract) {
                return;
            }
            boolean isUpdateSerial = !Objects.equals(taxHeader.getSerial(), contract.getTaxSerial());
            boolean isUpdateHeader = !Objects.equals(taxHeader.getHeader(), contract.getBusinessLicenseName());
            if (isUpdateSerial || isUpdateHeader) {
                Contract newContract = new Contract();
                newContract.setId(contract.getId());
                if (isUpdateSerial) {
                    newContract.setTaxSerial(taxHeader.getSerial());
                }
                if (isUpdateHeader) {
                    newContract.setBusinessLicenseName(taxHeader.getHeader());
                }
                newContract.setModifierId(AdminContext.getUser().getUserId());
                newContract.setModifierName(AdminContext.getUser().getUsername());
                log.info("【Contract】 更新营业执照信息,data:{}", JsonUtils.object2Json(newContract));
                contractMapper.updateContract(newContract);
            }

        }
    }

    /**
     * 合同参数初始化
     * @param contract
     */
    private void prepareContract(Contract contract) {
        if (contract.getOrderNo() == null) {
            contract.setOrderNo(StringUtils.EMPTY);
        }
        if (contract.getRealtyId() == null) {
            contract.setRealtyId(0L);
        }
        if (contract.getCustomerId() == null) {
            contract.setCustomerId(0L);
        }
        if (contract.getStatus() == null) {
            contract.setStatus(ContractStatus.SUSPEND.ordinal());
        }
        if (contract.getTemporaryRent() == null) {
            contract.setTemporaryRent(Boolean.FALSE);
        }
        if (contract.getRenamed() == null) {
            contract.setRenamed(Boolean.FALSE);
        }
        if (contract.getCategory() == null) {
            contract.setCategory(0);
        }
        // 合同编号
        if (StringUtils.isBlank(contract.getContractNo())) {
            String contractNoPrefix = LocalDate.now().format(DateTimeFormatter.ofPattern(RealtyConst.SERIAL_DATE_FORMAT));
            contract.setContractNo(prepareContractNo(contractNoPrefix, contractNoPostfixLength, fillChar));
        }
    }

    private void prepareContractExt(Contract contract, ContractExt ext) {
        // 合同id
        ext.setContractId(contract.getId());
        // 修改用户
        ext.setCreatorId(contract.getModifierId());
        ext.setCreatorName(contract.getModifierName());
        ext.setModifierId(contract.getModifierId());
        ext.setModifierName(contract.getModifierName());

        if (ext.getCostType() == null) {
            ext.setCostType(0);
        }
    }

    /**
     * 生成合同号
     * @param prefix
     * @param length
     * @param fillChar
     * @return
     */
    private String prepareContractNo(String prefix, int length, char fillChar) {
        // redis 合同序列号
        String cacheKey = String.format(RealtyConst.Cache.KEY_CONTRACT_NO, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, RealtyConst.Cache.TTL_2D);

        // db 合同序列号
        Long dbSerial = getMaxContractNo(prefix);
        if (result < dbSerial) {
            result = dbSerial + 1;
            RedisUtils.set(cacheKey, result, RealtyConst.Cache.TTL_2D);
        }

        return prefix.concat(StringUtils.fixLength(String.valueOf(result), length, fillChar));
    }

    /**
     * 最大合同编号
     * @param prefix
     * @return
     */
    private Long getMaxContractNo(String prefix) {
        String maxContractNo = contractMapper.findMaxContractNo(prefix);
        return StringUtils.isBlank(maxContractNo) ? 0L : NumberUtils.parseLong(maxContractNo.substring(prefix.length()), 0L);
    }

    /**
     * 比较合同费项
     * @param src
     * @param target
     * @return
     */
    private DataSepDto<ContractFee> sepContractFee(List<ContractFee> src, List<ContractFee> target) {
        DataSepDto<ContractFee> result = new DataSepDto<>();
        if (CollectionUtils.isEmpty(target)) {
            // 目标记录为空，删掉所有原始记录
            result.setRemoveList(src);

        } else if (CollectionUtils.isEmpty(src)) {
            // 原始记录为空，新增所有目标记录
            result.setAddList(target);

        } else {
            // 新增所有在原始记录中没有的目标记录
            List<ContractFee> addList = target.stream()
                    .filter(x -> !isContractFeeExist(src, x))
                    .collect(Collectors.toList());
            // 移除所有在目标记录中没有的原始记录
            List<ContractFee> removeList = src.stream()
                    .filter(x -> !isContractFeeExist(target, x))
                    .collect(Collectors.toList());
            result.setAddList(addList);
            result.setRemoveList(removeList);
        }
        return result;
    }

    /**
     * 合同费项是否存在
     * @param list
     * @param item
     * @return
     */
    private boolean isContractFeeExist(List<ContractFee> list, ContractFee item) {
        return list.contains(item);
    }

    /**
     * 合同校验
     * @param contract
     */
    private void checkContract(Contract contract) {
        // 判断是否续签，判断上一份合同的日期和当前是否重叠
        Contract lastContract = findRealtyContract(contract.getId(), contract.getRealtyId(), contract.getType());
        if (lastContract != null && contract.getStartDate().isBefore(lastContract.getEndDate())) {
            throw new BusinessException("开始日期不能小于现有合同到期日");
        }

        // 租赁合同
        ContractType contractType = ContractType.fromValue(contract.getType());
        if (contractType != ContractType.LEASE) {
            return;
        }
        // 如果是有物业合同的，要判断是否有返租、代租、代收租合同
        if (findRealtyContract(contract.getRealtyId(), ContractType.ESTATE.getValue()) != null) {
            Realty realty = realtyService.findById(contract.getRealtyId());
            if (realty != null && rentAnywayRealties.contains(realty.getSerialNo())) {
                log.info("物业无返祖代租合同，但仍可以租 {} ", realty.getSerialNo());
                return;
            }

            // 返租、代租、代收租合同的最大结束日期
            LocalDate md = contractMapper.findRealtyMaxRentableDate(contract.getRealtyId());
            if (md == null) {
                throw new BusinessException("该物业还没有返祖或代租合同");
            }
            if (contract.getEndDate().isAfter(md)) {
                throw new BusinessException("结束日期不能大于返祖或代租合同到期日");
            }
        }
    }
}

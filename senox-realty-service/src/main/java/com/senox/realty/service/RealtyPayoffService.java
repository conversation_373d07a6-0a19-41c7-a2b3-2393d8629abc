package com.senox.realty.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.RealtyFee;
import com.senox.realty.convert.RealtyPayoffConvertor;
import com.senox.realty.domain.Contract;
import com.senox.realty.domain.Realty;
import com.senox.realty.domain.RealtyPayoff;
import com.senox.realty.domain.RealtyPayoffItem;
import com.senox.realty.mapper.RealtyPayoffMapper;
import com.senox.realty.vo.RealtyPayoffSearchVo;
import com.senox.realty.vo.RealtyPayoffVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/11/23 11:16
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RealtyPayoffService {

    private final RealtyPayoffMapper payoffMapper;
    private final ContractService contractService;
    private final RealtyService realtyService;
    private final RealtyPayoffConvertor payoffConvertor;


    /**
     * 保存应付账单
     * @param vo
     * @param operator
     */
    public void savePayoff(RealtyPayoffVo vo, AdminUserDto operator) {
        // 应付账单
        RealtyPayoff payoff = buildPayoff(vo, operator);
        // 应付账单明细
        List<RealtyPayoffItem> items = Collections.singletonList(buildPayoffItem(vo.getRentAmount(), RealtyFee.RENT));

        // 查找已生成的账单
        RealtyPayoff dbItem = findPayoff(vo);
        if (dbItem == null) {
            addPayoff(payoff, items);
        } else {
            if (BillStatus.PAID.getStatus() == dbItem.getStatus()) {
                log.info("【应付账单】账单已支付，不更新账单 {}。", JsonUtils.object2Json(dbItem));
            }

            payoff.setId(dbItem.getId());
            updatePayoff(payoff, items);
        }
    }

    /**
     * 添加应付账单
     * @param payoff
     * @param items
     */
    public void addPayoff(RealtyPayoff payoff, List<RealtyPayoffItem> items) {
        // 根据应付账单费项明细，计算总金额
        payoff.setAmount(checkItems(items));

        // 应付账单初始化
        preparePayoff(payoff);
        int result = payoffMapper.addPayoff(payoff);

        if (result > 0) {
            // 应付账单明细初始化
            preparePayoffItems(payoff, items);
            payoffMapper.batchAddItems(items);
        }
    }

    /**
     * 更新应付账单
     * @param payoff
     * @param items
     */
    public void updatePayoff(RealtyPayoff payoff, List<RealtyPayoffItem> items) {
        // 根据应付账单费项明细，计算总金额
        payoff.setAmount(checkItems(items));
        // 应付账单初始化
        preparePayoff(payoff);
        preparePayoffItems(payoff, items);

        // 比对费项
        List<RealtyPayoffItem> dbItems = payoffMapper.listPayoffItems(payoff.getId());
        DataSepDto<RealtyPayoffItem> sepData = compareAndSeparateItem(dbItems, items);

        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            payoffMapper.batchAddItems(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            payoffMapper.batchUpdateItems(sepData.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            List<Long> delIds = sepData.getRemoveList().stream().map(RealtyPayoffItem::getId).collect(Collectors.toList());
            payoffMapper.batchDelItems(payoff.getId(), delIds);
        }
        updatePayoff(payoff);
    }

    /**
     * 更新应付账单
     * @param payoff
     */
    public void updatePayoff(RealtyPayoff payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            return;
        }
        payoffMapper.updatePayoff(payoff);
    }

    /**
     * 删除应付账单
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deletePayoff(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        RealtyPayoffVo payoff = findDetailById(id);
        if (payoff == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BillStatus.PAID.getStatus() == payoff.getStatus()) {
            throw new BusinessException("操作失败，账单已支付！");
        }

        if (payoffMapper.deletePayoff(id) > 0) {
            payoffMapper.deleteItems(id);
        }
    }

    /**
     * 查找应付账单详情
     * @param id
     * @return
     */
    public RealtyPayoffVo findDetailById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? payoffMapper.findDetailById(id) : null;
    }

    /**
     * 根据合同查找
     * @param year
     * @param month
     * @param contractNo
     * @return
     */
    public List<RealtyPayoff> findByContractNo(Integer year, Integer month, String contractNo) {
        return StringUtils.isBlank(contractNo) ? Collections.emptyList() : payoffMapper.findByContractNo(year, month, contractNo);
    }

    /**
     * 应付账单合计
     * @param search
     * @return
     */
    public RealtyPayoffVo sumPayoff(RealtyPayoffSearchVo search) {
        return payoffMapper.sumPayoff(search);
    }

    /**
     * 应付账单列表
     * @param search
     * @return
     */
    public PageResult<RealtyPayoffVo> listPayoffPage(RealtyPayoffSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }

        // 总记录数
        int totalSize = payoffMapper.countPayoff(search);
        search.prepare();

        if (totalSize <= search.getOffset()) {
            return PageResult.emptyPage();
        }

        // list
        List<RealtyPayoffVo> resultList = payoffMapper.listPayoff(search);
        return PageUtils.resultPage(search, totalSize, resultList);
    }


    /**
     * 查找应付账0单
     * @param vo
     * @return
     */
    private RealtyPayoff findPayoff(RealtyPayoffVo vo) {
        if (!WrapperClassUtils.biggerThanLong(vo.getId(), 0L)) {
            return null;
        }
        return payoffMapper.findById(vo.getBillYear(), vo.getBillMonth(), vo.getId());

    }

    /**
     * 校验应付账单明细
     * @param items
     * @return
     */
    private BigDecimal checkItems(List<RealtyPayoffItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return BigDecimal.ZERO;
        }

        if (items.stream().map(RealtyPayoffItem::getFeeId).distinct().count() != items.size()) {
            throw new InvalidParameterException("重复的费项");
        }

        return items.stream().map(RealtyPayoffItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 应付账单初始化
     * @param payoff
     */
    private void preparePayoff(RealtyPayoff payoff) {
        payoff.setBillYearMonth(DateUtils.formatYearMonth(payoff.getBillYear(), payoff.getBillMonth(), DateUtils.PATTERN_YEAR_MONTH));
        payoff.setAmount(DecimalUtils.nullToZero(payoff.getAmount()));

        BillStatus billStatus = BillStatus.fromStatus(payoff.getStatus());
        billStatus = billStatus == null ? BillStatus.INIT : billStatus;
        payoff.setStatus(billStatus.getStatus());

        if (BillStatus.PAID == billStatus && payoff.getPaidTime() == null) {
            payoff.setPaidTime(LocalDateTime.now());
        }
    }

    /**
     * 应付账单明细初始化
     * @param payoff
     * @param items
     */
    private void preparePayoffItems(RealtyPayoff payoff, List<RealtyPayoffItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        items.forEach(x -> preparePayoffItem(payoff, x));
    }

    /**
     * 应付账单明细初始化
     * @param payoff
     * @param item
     */
    private void preparePayoffItem(RealtyPayoff payoff, RealtyPayoffItem item) {
        item.setAmount(DecimalUtils.nullToZero(item.getAmount()));
        // 状态
        if (item.getStatus() == null) {
            item.setStatus(payoff.getStatus() == null ? BillStatus.INIT.getStatus() : payoff.getStatus());
        }
        // 费项名
        if (StringUtils.isBlank(item.getFeeName())) {
            RealtyFee fee = RealtyFee.fromFeeId(item.getFeeId().intValue());
            item.setFeeName(fee == null ? StringUtils.EMPTY : fee.getName());
        }

        item.setBillId(payoff.getId());
        item.setCreatorId(payoff.getModifierId());
        item.setCreatorName(payoff.getModifierName());
        item.setModifierId(payoff.getModifierId());
        item.setModifierName(payoff.getModifierName());
    }

    /**
     * 构建应付账单
     * @param vo
     * @param operator
     * @return
     */
    private RealtyPayoff buildPayoff(RealtyPayoffVo vo, AdminUserDto operator) {
        RealtyPayoff result = payoffConvertor.vo2Do(vo);

        // 操作员信息
        if (operator != null) {
            result.setCreatorId(operator.getUserId());
            result.setCreatorName(operator.getUsername());
            result.setModifierId(operator.getUserId());
            result.setModifierName(operator.getUsername());
        }

        // 合同
        Contract contract = contractService.findByContractNo(result.getContractNo());
        if (contract == null) {
            log.warn("【应付账单】保存应付账单失败，合同 {} 不存在", result.getContractNo());
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "合同不存在");
        }
        result.setRealtyId(contract.getRealtyId());

        // 物业号校验
        if (!StringUtils.isBlank(vo.getRealtySerial())) {
            Realty realty = realtyService.findBySerial(vo.getRealtySerial());
            if (realty == null || !Objects.equals(realty.getId(), result.getRealtyId())) {
                log.warn("【应付账单】保存应付账单失败，合同 {} 与物业 {}不匹配", result.getContractNo(), vo.getRealtySerial());
                throw new BusinessException("合同与物业不匹配");
            }
        }
        return result;
    }

    /**
     * 构建应付账单明细
     * @param amount
     * @param fee
     * @return
     */
    private RealtyPayoffItem buildPayoffItem(BigDecimal amount, RealtyFee fee) {
        if (fee == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "无效的费项");
        }

        RealtyPayoffItem result = new RealtyPayoffItem();
        result.setAmount(DecimalUtils.nullToZero(amount));
        result.setFeeId((long) fee.getFeeId());
        result.setFeeName(fee.getName());
        return result;
    }

    /**
     * 比对费项明细
     * @param srcItems
     * @param targetItems
     * @return
     */
    private DataSepDto<RealtyPayoffItem> compareAndSeparateItem(List<RealtyPayoffItem> srcItems, List<RealtyPayoffItem> targetItems) {
        // 比较结果
        List<RealtyPayoffItem> addItems = null;
        List<RealtyPayoffItem> updateItems = null;
        List<RealtyPayoffItem> delItems = null;

        if (CollectionUtils.isEmpty(targetItems)) {
            delItems = srcItems;

        } else if (CollectionUtils.isEmpty(srcItems)) {
            addItems = targetItems;

        } else {
            addItems = new ArrayList<>(targetItems.size());
            updateItems = new ArrayList<>(targetItems.size());
            for (RealtyPayoffItem targetItem : targetItems) {
                RealtyPayoffItem item = findItemInList(srcItems, targetItem);
                if (item != null) {
                    targetItem.setId(item.getId());
                    updateItems.add(targetItem);
                } else {
                    addItems.add(targetItem);
                }
            }

            delItems = srcItems.stream()
                    .filter(x -> findItemInList(targetItems, x) == null)
                    .collect(Collectors.toList());
        }

        DataSepDto<RealtyPayoffItem> result = new DataSepDto<>();
        result.setAddList(addItems);
        result.setUpdateList(updateItems);
        result.setRemoveList(delItems);
        return result;
    }

    /**
     * 在费项明细中查找记录
     * @param list
     * @param item
     * @return
     */
    private RealtyPayoffItem findItemInList(List<RealtyPayoffItem> list, RealtyPayoffItem item) {
        return list.stream()
                .filter(x -> Objects.equals(x.getBillId(), item.getBillId()) && Objects.equals(x.getFeeId(), item.getFeeId()))
                .findFirst().orElse(null);
    }
}

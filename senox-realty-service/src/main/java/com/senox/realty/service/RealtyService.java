package com.senox.realty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.realty.constant.RealtyNature;
import com.senox.realty.domain.*;
import com.senox.realty.mapper.RealtyExtMapper;
import com.senox.realty.mapper.RealtyMapper;
import com.senox.realty.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/16 14:01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RealtyService extends ServiceImpl<RealtyMapper, Realty> {

    private final RealtyExtMapper realtyExtMapper;
    private final RealtyAliasService realtyAliasService;
    private final WaterElectricPriceTypeService priceTypeService;

    /**
     * 添加物业
     *
     * @param realty
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addRealty(Realty realty, RealtyExt ext) {
        prepareRealty(realty);
        // 校验编码唯一性
        if (checkRealtyExists(realty.getId(), realty.getSerialNo())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "已存在相同的物业编码");
        }

        realty.setCreateTime(LocalDateTime.now());
        realty.setModifiedTime(LocalDateTime.now());
        boolean result = save(realty);

        // 扩展信息
        if (result && ext != null) {
            prepareRealtyExt(realty, ext);
            prepareExtReadings(ext);
            realtyExtMapper.addRealtyExt(ext);
        }
        return result ? realty.getId() : 0L;
    }

    /**
     * 更新物业
     *
     * @param realty
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRealty(Realty realty, RealtyExt ext) {
        if (!WrapperClassUtils.biggerThanLong(realty.getId(), 0L)) {
            return false;
        }
        // 校验编码唯一性
        if (!StringUtils.isBlank(realty.getSerialNo())) {
            if (checkRealtyExists(realty.getId(), realty.getSerialNo())) {
                throw new BusinessException(ResultConst.DUPLICATE_ERROR, "已存在相同的物业编码");
            }
            realty.setOrderNum((prepareOrderNum(realty.getSerialNo()) + 1) % 2);
        }

        realty.setCreatorId(null);
        realty.setCreatorName(null);
        realty.setCreateTime(null);
        realty.setModifiedTime(null);
        boolean result = updateById(realty);

        // 扩展信息
        if (result && ext != null) {
            prepareRealtyExt(realty, ext);
            if (findExtByRealtyId(realty.getId()) != null) {
                realtyExtMapper.updateRealtyExt(ext);
            } else {
                prepareExtReadings(ext);
                realtyExtMapper.addRealtyExt(ext);
            }
        }
        return result;
    }

    /**
     * 更新水电读数
     * @param list
     */
    public void updateRealtyReadings(List<RealtyReadingsVo> list) {
        batchUpdateRealtyReadings(list);
        realtyAliasService.batchUpdateAliasReadings(list);
    }

    private void batchUpdateRealtyReadings(List<RealtyReadingsVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<RealtyReadingsVo> readings = list.stream().filter(x -> !BooleanUtils.isTrue(x.getAlias())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(readings)) {
            return;
        }

        realtyExtMapper.batchUpdateRealtyReadings(readings);
    }

    /**
     * 根据合同更新水电单价
     * @param contract
     */
    public void updateEnergyPriceByContract(ContractExt contract) {
        if (!WrapperClassUtils.biggerThanLong(contract.getContractId(), 0L)
                || (contract.getElectricPriceType() == null && contract.getWaterPriceType() == null)) {
            return;
        }

        WaterElectricPriceType wpt = contract.getWaterPriceType() == null
                ? null : priceTypeService.findById(Long.valueOf(contract.getWaterPriceType()));
        WaterElectricPriceType ept = contract.getElectricPriceType() == null
                ? null : priceTypeService.findById(Long.valueOf(contract.getElectricPriceType()));
        if (wpt == null && ept == null) {
            return;
        }

        RealtyExt re = new RealtyExt();
        re.setWaterPrice(wpt == null ? null : wpt.getPrice());
        re.setElectricPrice(ept == null ? null : ept.getPrice());
        re.setModifierId(contract.getModifierId());
        re.setModifierName(contract.getModifierName());
        realtyExtMapper.updateEnergyPriceByContractId(contract.getContractId(), re);
    }


    /**
     * 根据id查找物业
     * @param id
     * @return
     */
    public Realty findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 根据编号查找物业
     * @param serial
     * @return
     */
    public Realty findBySerial(String serial) {
        if (StringUtils.isBlank(serial)) {
            return null;
        }

        LambdaQueryWrapper<Realty> queryWrapper = new QueryWrapper<Realty>().lambda().eq(Realty::getSerialNo, serial);
        return baseMapper.selectOne(queryWrapper);
    }

    /**
     * 查找物业扩展信息
     *
     * @param realtyId
     * @return
     */
    public RealtyExt findExtByRealtyId(Long realtyId) {
        return WrapperClassUtils.biggerThanLong(realtyId, 0L) ? realtyExtMapper.findByRealtyId(realtyId) : null;
    }

    /**
     * 查找物业及业主信息
     *
     * @param id
     * @return
     */
    public RealtyVo findWithOwnerById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return getBaseMapper().findWithOwnerById(id);
    }

    /**
     * 物业合计
     * @param search
     * @return
     */
    public int countRealty(RealtySearchVo search) {
        return getBaseMapper().countRealty(search);
    }

    /**
     * 物业列表
     * @param search
     * @return
     */
    public List<Realty> listRealty(RealtySearchVo search) {
        return getBaseMapper().listRealty(search);
    }

    /**
     * 物业最新水电读数
     * @param realtyId
     * @return
     */
    public List<RealtyReadingsVo> listReadings(Long realtyId) {
        Realty realty = findById(realtyId);
        if (realty == null) {
            return Collections.emptyList();
        }

        // 物业水电读数
        List<RealtyReadingsVo> list = getBaseMapper().listReadings(Collections.singletonList(realty.getSerialNo()));

        // 物业副档
        List<RealtyAlias> aliasList = realtyAliasService.listRealtyAlias(realtyId);

        List<RealtyReadingsVo> resultList = null;
        if (!CollectionUtils.isEmpty(aliasList)) {
            resultList = new ArrayList<>(list.size() + aliasList.size());
            resultList.addAll(list);
            resultList.addAll(aliasList.stream().map(this::prepareRealtyReadings).collect(Collectors.toList()));
        } else {
            resultList = list;
        }

        return resultList;
    }

    /**
     * 根据物业编号获取物业最新水电读数
     *
     * @param realtySerials 物业编号列表
     * @return 物业最新水电读数列表
     */
    public List<RealtyReadingsVo> listReadings(List<String> realtySerials) {
        if (CollectionUtils.isEmpty(realtySerials)) {
            return Collections.emptyList();
        }
        return getBaseMapper().listReadings(realtySerials);
    }

    /**
     * 根据物业编号查询能源信息
     * @param realtySerials
     * @return
     */
    public List<RealtyEnergyVo> listEnergy(List<String> realtySerials) {
        if (CollectionUtils.isEmpty(realtySerials)) {
            return Collections.emptyList();
        }
        return realtyExtMapper.listEnergyBySerials(realtySerials);
    }

    /**
     * 根据物业编号查询能源信息
     * @param realtySerials
     * @return
     */
    public List<RealtyEnergyVo> listEnergyBySubSerials(List<String> realtySerials) {
        if (CollectionUtils.isEmpty(realtySerials)) {
            return Collections.emptyList();
        }
        return realtyExtMapper.listEnergyBySubSerials(realtySerials);
    }

    /**
     * 物业编号是否存在
     *
     * @param id
     * @param serialNo
     * @return
     */
    private boolean checkRealtyExists(Long id, String serialNo) {
        if (StringUtils.isBlank(serialNo)) {
            return false;
        }
        Long dbId = getBaseMapper().findIdBySerialNo(serialNo);
        if (!WrapperClassUtils.biggerThanLong(dbId, 0L)) {
            return false;
        }

        return !WrapperClassUtils.biggerThanLong(id, 0L) || !Objects.equals(id, dbId);
    }


    private void prepareRealty(Realty realty) {
        if (Objects.isNull(realty.getNature())) {
            realty.setNature(RealtyNature.UNKNOWN.ordinal());
        }
        if (Objects.isNull(realty.getArea())) {
            realty.setArea(BigDecimal.ZERO);
        }
        if (Objects.isNull(realty.getRegionId())) {
            realty.setRegionId(0L);
        }
        if (Objects.isNull(realty.getStreetId())) {
            realty.setStreetId(0L);
        }
        if (Objects.isNull(realty.getProfessionId())) {
            realty.setProfessionId(0L);
        }
        if (Objects.isNull(realty.getOwnerId())) {
            realty.setOwnerId(0L);
        }
        if (!StringUtils.isBlank(realty.getSerialNo())) {
            realty.setOrderNum((prepareOrderNum(realty.getSerialNo()) + 1) % 2);
        } else {
            realty.setOrderNum(0);
        }
    }

    private void prepareRealtyExt(Realty realty, RealtyExt ext) {
        if (ext == null) {
            return;
        }
        ext.setRealtyId(realty.getId());
        ext.setCreatorId(realty.getModifierId());
        ext.setCreatorName(realty.getModifierName());
        ext.setModifierId(realty.getModifierId());
        ext.setModifierName(realty.getModifierName());
    }

    private void prepareExtReadings(RealtyExt ext) {
        if (ext.getWaterReadings() == null) {
            ext.setWaterReadings(0);
        }
        if (ext.getElectricReadings() == null) {
            ext.setElectricReadings(0);
        }
        if (ext.getWaterPrice() == null) {
            ext.setWaterPrice(BigDecimal.ZERO);
        }
        if (ext.getElectricPrice() == null) {
            ext.setElectricPrice(BigDecimal.ZERO);
        }
        if (ext.getWaterConsumeUnit() == null) {
            ext.setWaterConsumeUnit(0);
        }
        if (ext.getElectricConsumeUnit() == null) {
            ext.setElectricConsumeUnit(0);
        }
    }

    private RealtyReadingsVo prepareRealtyReadings(RealtyAlias alias) {
        RealtyReadingsVo result = new RealtyReadingsVo();
        result.setRealtySerial(alias.getSerialNo());
        result.setWaterReadings(alias.getWaterReadings());
        result.setElectricReadings(alias.getElectricReadings());
        result.setAlias(Boolean.TRUE);
        return result;
    }

    /**
     * 物业数量
     * @return
     */
    public RealtyStatistics realtyStatistics() {
        int unRentCompanyRealtyNum = getBaseMapper().unRentCompanyRealtyNum();
        int rentCompanyRealtyNum = getBaseMapper().rentCompanyRealtyNum();
        log.info("【未出租公司数量】：{}， 【已出租公司数量】：{}", unRentCompanyRealtyNum, rentCompanyRealtyNum);
        RealtyStatistics statistics = new RealtyStatistics();
        statistics.setUnRentCompanyRealtyNum(unRentCompanyRealtyNum);
        statistics.setRentCompanyRealtyNum(rentCompanyRealtyNum);
        statistics.setRealtyNum(countRealty(new RealtySearchVo()));
        return statistics;
    }

    private static Integer prepareOrderNum(String serialNo) {
        String numberStr = serialNo.replaceAll("[^0-9]", "");
        return StringUtils.isBlank(numberStr) ? 0 : Integer.parseInt(numberStr);
    }

    /**
     * 添加物业税率
     * @param realtyTaxRate 税率参数
     */
    public void saveRealtyTaxRate(RealtyTaxRateVo realtyTaxRate) {
        if (null == realtyTaxRate || CollectionUtils.isEmpty(realtyTaxRate.getRealtySerials())) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        RealtySearchVo searchVo = new RealtySearchVo();
        searchVo.setPage(false);
        searchVo.setSerialNos(realtyTaxRate.getRealtySerials());
        List<Realty> realtyList = listRealty(searchVo);
        if (CollectionUtils.isEmpty(realtyList)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        List<Long> realtyIds = realtyList.stream().map(Realty::getId).collect(Collectors.toList());
        realtyExtMapper.updateRealtyExtTaxRate(realtyIds, realtyTaxRate.getTaxCode(), AdminContext.getUser());
    }

    /**
     * 取消物业税率
     *
     * @param realtyTaxRate 税率参数
     */
    public void cancelRealtyTaxRate(RealtyTaxRateVo realtyTaxRate) {
        RealtySearchVo searchVo = new RealtySearchVo();
        searchVo.setPage(false);
        searchVo.setSerialNos(realtyTaxRate.getRealtySerials());
        List<Realty> realtyList = listRealty(searchVo);
        if (CollectionUtils.isEmpty(realtyList)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        List<Long> realtyIds = realtyList.stream().map(Realty::getId).collect(Collectors.toList());
        realtyExtMapper.updateRealtyExtTaxRate(realtyIds, null, AdminContext.getUser());
    }

    /**
     * 物业税率列表
     *
     * @param search 查询参数
     * @return 返回列表
     */
    public List<RealtyVo> listRealtyTaxRate(RealtySearchVo search) {
        return baseMapper.listRealtyTaxRate(search);
    }

    /**
     * 物业税率分页列表
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<RealtyVo> pageListRealtyTaxRate(RealtySearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search,
                () -> baseMapper.countListRealtyTaxRate(search),
                () -> baseMapper.listRealtyTaxRate(search));
    }
}

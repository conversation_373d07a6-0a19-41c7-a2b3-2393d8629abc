package com.senox.realty.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.Fee;
import com.senox.realty.mapper.FeeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/15 13:44
 */
@Service
public class FeeService {

    @Autowired
    private FeeMapper feeMapper;

    /**
     * 添加费项
     * @param fee
     * @return
     */
    public Long addFee(Fee fee) {
        if (StringUtils.isBlank(fee.getName())) {
            return 0L;
        }
        if (fee.getAmount() == null) {
            fee.setAmount(BigDecimal.ZERO);
        }
        long result =  feeMapper.addFee(fee) > 0 ? fee.getId() : 0L;

        // clear cache
        if (result > 0L) {
            RedisUtils.del(RealtyConst.Cache.KEY_FEE);
        }
        return result;
    }

    /**
     * 更新费项
     * @param fee
     * @return
     */
    public boolean updateFee(Fee fee) {
        if (!WrapperClassUtils.biggerThanLong(fee.getId(), 0L)) {
            return false;
        }
        boolean result = feeMapper.updateFee(fee) > 0;

        // clear cache
        if (result) {
            RedisUtils.del(RealtyConst.Cache.KEY_FEE);
        }
        return result;
    }

    /**
     * 根据id查找费项
     * @param id
     * @return
     */
    public Fee findById(Long id) {
        if (id == null || id < 1L) {
            return null;
        }
        return feeMapper.findById(id);
    }

    /**
     * 根据费项名查找费项
     * @param name
     * @return
     */
    public Fee findByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return feeMapper.findByName(name);
    }

    /**
     * 查询所有费项
     * @return
     */
    public List<Fee> listAll() {
        List<Fee> resultList = null;

        // load from cache
        String cacheValue = RedisUtils.get(RealtyConst.Cache.KEY_FEE);
        if (!StringUtils.isBlank(cacheValue)) {
            resultList = JsonUtils.json2GenericObject(cacheValue, new TypeReference<List<Fee>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = feeMapper.listAll();

            // set cache
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(RealtyConst.Cache.KEY_FEE, JsonUtils.object2Json(resultList), RealtyConst.Cache.TTL_7D);
            }
        }

        return resultList;
    }

    /**
     * 费项map
     * @return
     */
    public Map<Long, Fee> listFeeMap() {
        List<Fee> list = listAll();
        return list.stream().collect(Collectors.toMap(Fee::getId, Function.identity()));
    }
}

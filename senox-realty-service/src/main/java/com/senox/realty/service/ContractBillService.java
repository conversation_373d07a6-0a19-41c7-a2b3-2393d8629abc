package com.senox.realty.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.constant.SystemParam;
import com.senox.common.constant.device.EnergyType;
import com.senox.common.domain.SystemSetting;
import com.senox.common.exception.BusinessException;
import com.senox.common.service.SystemSettingService;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminUserDto;
import com.senox.realty.config.RealtyConfig;
import com.senox.realty.constant.BillStatus;
import com.senox.realty.constant.ContractFeeCategory;
import com.senox.realty.constant.ContractStatus;
import com.senox.realty.constant.ContractType;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.constant.RealtyFee;
import com.senox.realty.convert.ContractConvertor;
import com.senox.realty.convert.WaterElectricPriceTypeConvertor;
import com.senox.realty.domain.*;
import com.senox.realty.event.ContractEnableEvent;
import com.senox.realty.event.RealtyBillChangedEvent;
import com.senox.realty.event.ResidentAccessAddEvent;
import com.senox.realty.event.ResidentAccessDelEvent;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.BillFeeChangeVo;
import com.senox.realty.vo.BillMonthVo;
import com.senox.realty.vo.ContractBillSearchVo;
import com.senox.realty.vo.ContractEnableDto;
import com.senox.realty.vo.ContractSearchVo;
import com.senox.realty.vo.ContractVo;
import com.senox.realty.vo.RealtyBillVo;
import com.senox.realty.vo.RealtyContractSuspendDto;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Month;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-9-13
 */
@Slf4j
@Service
@AllArgsConstructor
public class ContractBillService {

    private final RealtyConfig realtyConfig;
    private final RealtyService realtyService;
    private final RealtyBillService realtyBillService;
    private final RealtyPayoffService payoffService;
    private final ContractService contractService;
    private final ContractConvertor contractConvertor;
    private final RealtyWeService realtyWeService;
    private final RealtyBillWeService realtyBillWeService;
    private final RealtyBillPenaltyService penaltyService;
    private final SystemSettingService settingService;
    private final WaterElectricPriceTypeService waterElectricPriceTypeService;
    private final WaterElectricPriceTypeConvertor waterElectricPriceTypeConvertor;
    private final ApplicationEventPublisher publisher;

    /**
     * 生成应收账单
     * @param month
     * @return
     */
    public void buildAndSaveBill(BillMonthVo month, AdminUserDto operator) {
        final String buildLock = String.format(RealtyConst.Cache.KEY_REALTY_BILL_BUILD, month.getYear(), month.getMonth());
        if (!RedisUtils.lock(buildLock, RealtyConst.Cache.TTL_1H)) {
            throw new BusinessException("【应收账单】系统处理中，请稍侯。");
        }

        log.info("【应收账单】生成{}-{}应收账单开始...", month.getYear(), month.getMonth());
        long execStartTime = System.currentTimeMillis();
        try {
            // 取当月最后1天作为账单日
            LocalDate billDate = DateUtils.parseDate(month.getYear(), month.getMonth() + 1, 1, true).minusDays(1);
            // 待生成应收账单的合同列表
            List<ContractVo> contracts = listRealtyBillContract(billDate, month.getContractNo());
            // 排除已生成账单的合同
            Map<String, List<ContractVo>> contractMap = contracts.stream().collect(Collectors.groupingBy(ContractVo::getRealtySerial));
            if (CollectionUtils.isEmpty(contractMap)) {
                log.info("【应收账单】生成{}-{}应收账单 暂无待生成账单合同.", month.getYear(), month.getMonth());
                return;
            }

            // 遍历生成账单
            List<CompletableFuture<Void>> futures = new ArrayList<>(contractMap.size() / RealtyConst.BATCH_SIZE_1000 + 1);
            for (int index = 0; index < contractMap.size(); index += RealtyConst.BATCH_SIZE_1000) {
                List<Map.Entry<String, List<ContractVo>>> list = contractMap.entrySet().stream().skip(index).limit(RealtyConst.BATCH_SIZE_1000).collect(Collectors.toList());
                // 多线程异步处理生成账单
                futures.add(CompletableFuture.runAsync(() -> {
                    for (Map.Entry<String, List<ContractVo>> entry : list) {
                        buildAndSaveBill(entry.getValue(), billDate, operator);
                    }
                }));
            }

            // 等待所有线程执行完毕
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            settingService.updateBatchById(Arrays.asList(
                    new SystemSetting(SystemParam.REALTY_BILL_YEAR.name(), month.getYear().toString()),
                    new SystemSetting(SystemParam.REALTY_BILL_MONTH.name(), month.getMonth().toString())
            ));
        } finally {
            log.info("【应收账单】生成{}-{}应收账单结束，耗时{}。", month.getYear(), month.getMonth(), System.currentTimeMillis() - execStartTime);
            RedisUtils.del(buildLock);
        }
    }

    /**
     * 生成应付账单
     * @param month
     * @param operator
     */
    public void buildAndSavePayoff(BillMonthVo month, AdminUserDto operator) {
        final String buildLock = String.format(RealtyConst.Cache.KEY_REALTY_PAYOFF_BUILD, month.getYear(), month.getMonth());
        if (!RedisUtils.lock(buildLock, RealtyConst.Cache.TTL_1H)) {
            throw new BusinessException("【应付账单】系统处理中，请稍侯。");
        }

        log.info("【应付账单】生成{}-{}应付账单开始...", month.getYear(), month.getMonth());
        long execStartTime = System.currentTimeMillis();
        try {
            // 取当月最后1天作为账单日
            LocalDate billDate = DateUtils.parseDate(month.getYear(), month.getMonth() + 1, 1, true).minusDays(1);
            // 待生成应收账单的合同列表
            List<ContractVo> contracts = listPayoffContract(billDate, month.getContractNo());
            // 防止一个物业生成多次账单
            Map<String, List<ContractVo>> contractMap = contracts.stream().collect(Collectors.groupingBy(ContractVo::getRealtySerial));
            if (CollectionUtils.isEmpty(contractMap)) {
                log.info("【应付账单】生成{}-{}应付账单 暂无待生成账单合同.", month.getYear(), month.getMonth());
                return;
            }

            // 遍历生成账单
            List<CompletableFuture<Void>> futures = new ArrayList<>(contractMap.size() / RealtyConst.BATCH_SIZE_1000 + 1);
            for (int index = 0; index < contractMap.size(); index += RealtyConst.BATCH_SIZE_1000) {
                List<Map.Entry<String, List<ContractVo>>> list = contractMap.entrySet().stream().skip(index).limit(RealtyConst.BATCH_SIZE_1000).collect(Collectors.toList());
                // 多线程异步处理生成账单
                futures.add(CompletableFuture.runAsync(() -> {
                    for (Map.Entry<String, List<ContractVo>> entry : list) {
                        buildAndSavePayoff(entry.getValue().get(0), billDate, operator);
                    }
                }));
            }

            // 等待所有线程执行完毕
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        } finally {
            log.info("【应付账单】生成{}-{}应付账单结束，耗时{}。", month.getYear(), month.getMonth(), System.currentTimeMillis() - execStartTime);
            RedisUtils.del(buildLock);
        }
    }

    /**
     * 更新物业账单水电数据
     * @param billTime
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveRealtyBillWeData(BillMonthVo billTime, AdminUserDto operator) {
        BillMonthVo weTime = calRealtyWeTime(billTime.getYear(), billTime.getMonth());

        // 关联水电账单及物业账单
        realtyBillWeService.updateWeBillId(weTime);
        // 更新物业账单水电明细
        realtyBillService.updateBillWeAmount(billTime);
        realtyBillService.updateBillWeDefaultAttr(billTime, weTime);
        // 更新物业账单合计金额
        realtyBillService.updateBillAmount(billTime);

        // 没有匹配到应收账单的水电账单
        saveRealtyBillWithWeDataOnly(billTime, operator);

        // 计算滞纳金
        penaltyService.calBillPenaltyAsync();
    }

    /**
     * 账单id更新物业
     * @param billId
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveRealtyBillWeData(Long billId) {
        BillMonthVo billMonth = new BillMonthVo();
        billMonth.setBillId(billId);
        realtyBillService.updateBillWeAmount(billMonth);
        realtyBillService.updateBillAmount(billMonth);

        // 计算滞纳金
        RealtyBillVo bill = realtyBillService.findById(billId);
        if (bill != null) {
            penaltyService.calBillPenalty(bill.getContractNo());
        }
    }

    /**
     * 没有匹配到应收账单的水电账单生成应收
     * @param billTime
     */
    public void saveRealtyBillWithWeDataOnly(BillMonthVo billTime, AdminUserDto operator) {
        BillMonthVo weTime = calRealtyWeTime(billTime.getYear(), billTime.getMonth());
        weTime.setRealtySerial(billTime.getRealtySerial());
        // 合同转物业号
        if (!StringUtils.isBlank(billTime.getContractNo()) && StringUtils.isBlank(weTime.getRealtySerial())) {
            Contract contract = contractService.findByContractNo(billTime.getContractNo());
            if (contract != null) {
                Realty realty = realtyService.findById(contract.getRealtyId());
                weTime.setRealtySerial(realty == null ? billTime.getRealtySerial() : realty.getSerialNo());
            }
        }
        // 未匹配账单的水电账单
        List<RealtyBillWe> weBill = realtyBillWeService.listNonmatchWeBill(weTime);
        saveRealtyBillWithWeDataOnly(billTime, weBill, operator);
    }

    public void saveRealtyBillWithWeDataOnly(BillMonthVo billTime, List<RealtyBillWe> weBillList, AdminUserDto operator) {
        if (CollectionUtils.isEmpty(weBillList)) {
            return;
        }

        Map<String, List<RealtyBillWe>> weBillMap = weBillList.stream().collect(Collectors.groupingBy(RealtyBillWe::getRealtySerial));
        for (Map.Entry<String, List<RealtyBillWe>> entry : weBillMap.entrySet()) {
            saveRealtyBillWithWeOnly(billTime, entry.getValue(), operator);
        }
    }

    /**
     * 根据水电账单直接生成应收账单
     * @param billMonth
     * @param weBills
     * @param operator
     */
    public void saveRealtyBillWithWeOnly(BillMonthVo billMonth, List<RealtyBillWe> weBills, AdminUserDto operator) {
        // 查找物业的租赁/物业合同
        String realtySerial = weBills.get(0).getRealtySerial();
        List<ContractVo> contracts = contractService.listEffectiveContractByRealtySerial(realtySerial,
                Arrays.asList(ContractType.LEASE.getValue(), ContractType.ESTATE.getValue()));
        if (CollectionUtils.isEmpty(contracts)) {
            log.info("{} 无租赁/物业合同，无需生成账单。", realtySerial);
            return;
        }

        ContractVo contract = contracts.get(0);
        LocalDate billDate = DateUtils.parseDate(billMonth.getYear(), billMonth.getMonth(), 1);
        billDate = billDate.plusMonths(1L).minusDays(1L);
        RealtyBill bill = buildContractBill(contract, billDate, operator);
        List<RealtyBillItem> billItems = buildContractBillItems(contract, billDate, true);
        fixRealtyWeBill(billItems, weBills);
        realtyBillService.addBill(bill, billItems);
        log.info("{} 根据水电账单生成应收账单。", realtySerial);

        // 计算滞纳金
        penaltyService.calBillPenalty(contract.getContractNo());
        // 回更账单id
        realtyBillWeService.updateWeBillId(weBills.stream().map(RealtyBillWe::getId).collect(Collectors.toList()), bill.getId());
    }

    /**
     * 启用合同
     * @param enableDto
     */
    @Transactional(rollbackFor = Exception.class)
    public void enableContract(ContractEnableDto enableDto) {
        ContractVo contract = contractService.findWithExtByContractNo(enableDto.getContractNo());
        if (contract == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (ContractStatus.EFFECTIVE == ContractStatus.fromValue(contract.getStatus())) {
            return;
        }

        ContractType contractType = ContractType.fromValue(contract.getType());
        if (contractType == ContractType.LEASE || contractType == ContractType.ESTATE) {
            checkContractEnergyPrice(contract);
        }

        // 启用合同
        contractService.enableContract(enableDto);

        // 应收账单生成的月份
        LocalDate billDate = DateUtils.getLastDateInMonth(enableDto.getEnableDate());
        LocalDate systemBillDate = findBillTimeFromSystemSetting();

        // 生成当前账单月应收/应付账单
        if (!contract.getStartDate().isAfter(billDate)) {
            // 租赁、物业合同生成应收账单
            buildAndSaveBill(contract, billDate, enableDto.getOperator(), true);
            // 返祖、代租、代收租合同产生应付账单
            buildAndSavePayoff(contract, billDate, enableDto.getOperator(), true);

            // 租赁合同启用，对应物业合同处理
            if (contractType == ContractType.LEASE) {
                buildEnableEstateBill(contract.getRealtyId(), contract.getStartDate(), billDate, enableDto.getOperator());
            }
        }

        // 月底启用合同，下个月应收已产生处理  （应付账单不需要自动生成）
        if (systemBillDate != null && !DateUtils.isSameYearMonth(billDate, systemBillDate)) {
            // 租赁、物业合同生成应收账单
            buildAndSaveBill(contract, systemBillDate, enableDto.getOperator(), true);
            // 租赁合同启用，对应物业合同处理
            if (contractType == ContractType.LEASE) {
                buildEnableEstateBill(contract.getRealtyId(), DateUtils.getFirstDateInMonth(systemBillDate), systemBillDate, enableDto.getOperator());
            }
        }

        publisher.publishEvent(new ContractEnableEvent(this, contract));
        //恢复用户门禁权限
        publisher.publishEvent(new ResidentAccessAddEvent(this, contract.getId(), contract.getContractNo()));
    }

    /**
     * 中止合同
     * @param suspendDto
     * @param weList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void suspendContract(RealtyContractSuspendDto suspendDto, List<RealtyWe> weList) {
        ContractVo contract = contractService.findWithExtByContractNo(suspendDto.getContractNo());
        // 停用合同
        suspendContract(contract, suspendDto);

        // 只有租赁合同才需要生成账单
        ContractType contractType = ContractType.fromValue(contract.getType());
        switch (contractType) {
            case LEASE:
                buildAndSaveSuspendBill(suspendDto, contract, weList);

                // 停用租赁合同需同时停用代租合同 (只关闭合同开始时间一样的代租合同)
                ContractVo rentProxyContract = findRentProxyContractByLeaseContract(contract);
                if (rentProxyContract != null) {
                    RealtyContractSuspendDto rentPoxySuspend = new RealtyContractSuspendDto();
                    rentPoxySuspend.setContractNo(rentProxyContract.getContractNo());
                    rentPoxySuspend.setSuspendDate(suspendDto.getSuspendDate());
                    rentPoxySuspend.setOperator(suspendDto.getOperator());
                    log.info("【停用合同】停用租赁合同 {} 需同时停用代租合同 (只关闭合同开始时间一样的代租合同) {}", suspendDto.getContractNo(), JsonUtils.object2Json(rentPoxySuspend));
                    suspendContract(rentPoxySuspend, Collections.emptyList());
                }
                break;
            case RENT_PROXY:
                buildAndSaveSuspendPayoff(contract, suspendDto);
                break;

            default:
                break;
        }
        //移除合同相关用户的门禁权限
        publisher.publishEvent(new ResidentAccessDelEvent(this, suspendDto.getContractNo()));
    }

    /**
     * 根据租赁合同查找代租合同
     * @param leaseContract
     * @return
     */
    private ContractVo findRentProxyContractByLeaseContract(ContractVo leaseContract) {
        ContractSearchVo search = new ContractSearchVo();
        search.setRealtyId(leaseContract.getRealtyId());
        search.setType(ContractType.RENT_PROXY.getValue());
        search.setStartDateBegin(leaseContract.getStartDate());
        search.setStartDateEnd(leaseContract.getStartDate());
        search.setStatus(ContractStatus.EFFECTIVE.ordinal());
        search.setOffset(0);
        search.setPageNo(1);
        search.setPageSize(10);

        List<ContractVo> list = contractService.listContract(search);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 停用合同
     * @param contract
     * @param suspendDto
     */
    private void suspendContract(ContractVo contract, RealtyContractSuspendDto suspendDto) {
        // 校验要停用的合同
        log.info("【停用合同】停用合同 {}...", contract.getContractNo());
        checkSuspendingContract(contract);

        // 停用合同
        int result = contractService.suspendContract(suspendDto);
        if (result < 1) {
            throw new BusinessException("【合同停用】停用合同失败 " + suspendDto.getContractNo());
        }
    }

    /**
     * 校验要停用的合同状态
     * @param contract
     */
    private void checkSuspendingContract(ContractVo contract) {
        if (Objects.isNull(contract)) {
            throw new BusinessException("合同不存在");
        }
        // 校验合同有效性
        if (ContractStatus.EFFECTIVE != ContractStatus.fromValue(contract.getStatus())) {
            throw new BusinessException("合同未启用");
        }
    }

    /**
     * 生成应收账单
     * @param contract
     * @param billDate
     * @param operator
     * @return
     */
    private RealtyBill buildContractBill(ContractVo contract, LocalDate billDate, AdminUserDto operator) {
        RealtyBill result = new RealtyBill();
        result.setBillYearMonth(DateUtils.formatYearMonth(billDate));
        result.setBillYear(billDate.getYear());
        result.setBillMonth(billDate.getMonthValue());
        result.setRealtyId(contract.getRealtyId());
        result.setContractNo(contract.getContractNo());
        result.setCreatorId(operator.getUserId());
        result.setCreatorName(operator.getUsername());
        result.setModifierId(operator.getUserId());
        result.setModifierName(operator.getUsername());

        // 滞纳金日期
        if (contract.getPenaltyStartDate() != null) {
            result.setPenaltyDate(DateUtils.parseDate(result.getBillYear(), result.getBillMonth(), contract.getPenaltyStartDate()));
        }
        return result;
    }

    /**
     * 生成账单明细
     * @param contract
     * @param billDate
     * @return
     */
    private List<RealtyBillItem> buildContractBillItems(ContractVo contract, LocalDate billDate) {
        return buildContractBillItems(contract, billDate, false);
    }

    /**
     * 生成账单明细
     * @param contract
     * @param billDate
     * @param isMrFeePaid 是否已支付管理费，租金
     * @return
     */
    private List<RealtyBillItem> buildContractBillItems(ContractVo contract, LocalDate billDate, boolean isMrFeePaid) {
        // 账单开始时间
        LocalDate billStartDate = DateUtils.getMaxDate(contract.getStartDate(), DateUtils.getFirstDateInMonth(billDate));

        List<ContractFee> contractFees = contractService.listContractFee(contract.getId());
        // 基础合同费项
        List<ContractFee> baseFees = contractFees.stream()
                .filter(x -> x.getCategory() == ContractFeeCategory.DEFAULT.ordinal())
                .collect(Collectors.toList());

        // 生成账单明细
        List<RealtyBillItem> resultList = new ArrayList<>(baseFees.size());
        for (ContractFee fee : baseFees) {
            // 费项阶段费用
            List<ContractFee> periodFees = contractFees.stream().filter(x -> x.getCategory() == ContractFeeCategory.PERIOD.ordinal())
                    .filter(x -> Objects.equals(x.getFeeId(), fee.getFeeId()))
                    .collect(Collectors.toList());
            resultList.add(buildContractBillItem(fee, periodFees, billStartDate, billDate, isMrFeePaid));
        }

        return resultList;
    }

    /**
     * 生成应收账单明细
     * @param fee
     * @param periodFees
     * @param billStartDate
     * @param billDate
     * @param isMrFeePaid
     * @return
     */
    private RealtyBillItem buildContractBillItem(ContractFee fee, List<ContractFee> periodFees, LocalDate billStartDate,
                                                 LocalDate billDate, boolean isMrFeePaid) {
        RealtyBillItem result = new RealtyBillItem();
        result.setFeeId(fee.getFeeId());

        if (isMrFeePaid && (fee.getFeeId() == RealtyFee.MANAGE.getFeeId() || fee.getFeeId() == RealtyFee.RENT.getFeeId())) {
            result.setAmount(BigDecimal.ZERO);

        } else {
            result.setAmount(calBillPeriodAmount(billStartDate, billDate, periodFees, fee.getAmount()).setScale(0, BigDecimal.ROUND_HALF_UP));
        }
        return result;
    }

    /**
     * 生成应付账单
     * @param contract
     * @param billDate
     * @param operator
     * @return
     */
    private RealtyPayoff buildPayoff(ContractVo contract, LocalDate billDate, AdminUserDto operator) {
        RealtyPayoff result = new RealtyPayoff();
        result.setBillYearMonth(DateUtils.formatYearMonth(billDate));
        result.setBillYear(billDate.getYear());
        result.setBillMonth(billDate.getMonthValue());
        result.setRealtyId(contract.getRealtyId());
        result.setContractNo(contract.getContractNo());
        result.setCreatorId(operator.getUserId());
        result.setCreatorName(operator.getUsername());
        result.setModifierId(operator.getUserId());
        result.setModifierName(operator.getUsername());
        return result;
    }

    /**
     * 生成应付账单明细
     * @param contract
     * @param billDate
     * @return
     */
    private List<RealtyPayoffItem> buildPayoffItems(ContractVo contract, LocalDate billDate) {
        List<ContractFee> contractFees = contractService.listContractFee(contract.getId());
        // 基础合同费项
        List<ContractFee> baseFees = contractFees.stream()
                .filter(x -> x.getCategory() == ContractFeeCategory.DEFAULT.ordinal())
                .collect(Collectors.toList());

        // 应付账单明细
        List<RealtyPayoffItem> resultList = new ArrayList<>(baseFees.size());

        for (ContractFee fee : baseFees) {
            // 费项阶段费用
            List<ContractFee> periodFees = contractFees.stream().filter(x -> x.getCategory() == ContractFeeCategory.PERIOD.ordinal())
                    .filter(x -> Objects.equals(x.getFeeId(), fee.getFeeId()))
                    .collect(Collectors.toList());
            resultList.add(buildPayoffItem(fee, periodFees, billDate));
        }

        // 非整月账单处理
        if (!CollectionUtils.isEmpty(resultList)) {
            long actualBillDays = calActualBilDays(contract, billDate);
            long billDays = calBillFullDays(billDate);
            if (actualBillDays != billDays) {
                for (RealtyPayoffItem item : resultList) {
                    item.setAmount(calRatioAmount(billDays, actualBillDays, item.getAmount()).setScale(0, BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        return resultList;
    }

    /**
     * 生成应付账单明细
     * @param fee
     * @param periodFees
     * @param billDate
     * @return
     */
    private RealtyPayoffItem buildPayoffItem(ContractFee fee, List<ContractFee> periodFees, LocalDate billDate) {
        RealtyPayoffItem result = new RealtyPayoffItem();
        result.setFeeId(fee.getFeeId());
        result.setAmount(fee.getAmount());

        // 期间费项
        ContractFee periodFee = specifyPeriodFee(periodFees, billDate);
        if (periodFee != null) {
            result.setAmount(periodFee.getAmount());
        }
        return result;
    }


    /**
     *
     * @param startDate
     * @param endDate
     * @param periodFees
     * @param defaultAmount
     * @return
     */
    private BigDecimal calBillPeriodAmount(LocalDate startDate, LocalDate endDate, List<ContractFee> periodFees, BigDecimal defaultAmount) {
        if (CollectionUtils.isEmpty(periodFees)) {
            return calBillAmount(startDate, endDate, defaultAmount);
        }

        // 查找区间费项
        ContractFee periodFee = specifyPeriodFee(periodFees, endDate);
        if (periodFee == null) {
            return calBillAmount(startDate, endDate, defaultAmount);
        }

        if (!startDate.isBefore(periodFee.getStartDate())) {
            return calBillAmount(startDate, endDate, periodFee.getAmount());
        }

        // 递归进行跨收费区间计费
        return DecimalUtils.add(calBillAmount(periodFee.getStartDate(), endDate, periodFee.getAmount()),
                calBillPeriodAmount(startDate, periodFee.getStartDate().minusDays(1L), periodFees, defaultAmount));
    }

    /**
     * 计算账单金额
     * @param startDate
     * @param endDate
     * @param amount
     * @return
     */
    private BigDecimal calBillAmount(LocalDate startDate, LocalDate endDate, BigDecimal amount) {
        long days = calBillDays(startDate, endDate);
        long fullDays = calBillFullDays(endDate);

        // 完整的账单期
        if (days == fullDays) {
            return amount;
        }

        // 计算天数，按比例计算金额
        return calRatioAmount(fullDays, days, amount);
    }

    /**
     * 实际账单日
     * @param contract
     * @param billDate
     * @return
     */
    private long calActualBilDays(ContractVo contract, LocalDate billDate) {
        LocalDate monthFirstDate = DateUtils.getFirstDateInMonth(billDate);
        LocalDate billStartDate = DateUtils.getMaxDate(monthFirstDate, contract.getStartDate());
        return DateUtils.getDaysBetween(billStartDate, billDate) + 1;
    }


    /**
     * 账单日
     * @param startDate
     * @param endDate
     * @return
     */
    private long calBillDays(LocalDate startDate, LocalDate endDate) {
        return DateUtils.getDaysBetween(startDate, endDate) + 1;
    }

    /**
     * 总账单日
     * @param billDate
     * @return
     */
    private long calBillFullDays(LocalDate billDate) {
        return DateUtils.getLastDateInMonth(billDate).getDayOfMonth();
    }

    /**
     * 按比例算金额
     * @param billDays
     * @param actualBillDays
     * @param amount
     */
    private BigDecimal calRatioAmount(long billDays, long actualBillDays, BigDecimal amount) {
        if (billDays == actualBillDays && DecimalUtils.isPositive(amount)) {
            return amount;
        }

        return DecimalUtils.multiple(amount, BigDecimal.valueOf(actualBillDays))
                .divide(BigDecimal.valueOf(billDays), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 获取阶段费项
     * @param periodFees
     * @param billDate
     * @return
     */
    private ContractFee specifyPeriodFee(List<ContractFee> periodFees, LocalDate billDate) {
        if (CollectionUtils.isEmpty(periodFees)) {
            return null;
        }

        // 取范围最小，最接近的期间费用
        return periodFees.stream()
                .filter(x -> DateUtils.isDateInRange(x.getStartDate(), x.getEndDate(), billDate))
                .min(Comparator.comparing(ContractFee::getStartDate).thenComparing(ContractFee::getEndDate))
                .orElse(null);
    }

    /**
     * 保存停用合同的水电数据
     * @param realtyBillId
     * @param contract
     * @param billDate
     * @param weList
     */
    private List<RealtyBillWe> buildAndSaveWeBill(Long realtyBillId, ContractVo contract, LocalDate billDate, List<RealtyWe> weList) {
        if (CollectionUtils.isEmpty(weList)) {
            log.info("【合同停用】合同 {} 停用未录入水电...", contract.getContractNo());
            return Collections.emptyList();
        }

        // 合同水电费项
        prepareEnergyPrice(contract, EnergyType.WATER);
        prepareEnergyPrice(contract, EnergyType.ELECTRIC);

        // 添加水电
        // 上次抄表日期取 合同开始日期、上一次抄表记录，系统默认 3者取最大
        // 本次抄表日期取 账单日期，当前日期，系统默认 3者取最小
        weList.forEach(x -> {
            x.setLastRecordDate(contract.getStartDate());
            x.setRecordDate(DateUtils.getMinDate(LocalDate.now(), billDate));
            // 低消
            x.setWaterBase(contract.getWaterPrice() != null && DecimalUtils.isPositive(contract.getWaterPrice().getPrice()) ? realtyConfig.getWaterBase() : 0);
            x.setElectricBase(contract.getElectricPrice() != null && DecimalUtils.isPositive(contract.getElectricPrice().getPrice()) ? realtyConfig.getElectricBase() : 0);
        });
        realtyWeService.batchAddWeData(weList, false);
        // 生成水电账单
        List<RealtyBillWe> resultList = realtyBillWeService.saveContractWeBill(realtyBillId, contract, weList);

        // 回更水电读数
        weList.forEach(x -> {
            for (RealtyBillWe item : resultList) {
                String serial = StringUtils.isBlank(item.getRealtyAliasSerial()) ? item.getRealtySerial() : item.getRealtyAliasSerial();
                if (Objects.equals(x.getRealtySerial(), serial)) {
                    x.setWeBillId(item.getId());
                    break;
                }
            }
        });
        realtyWeService.batchUpdateBillById(weList);
        return resultList;
    }

    /**
     * 月账单关联水电账单
     * @param billItems
     * @param weBills
     */
    private void fixRealtyWeBill(List<RealtyBillItem> billItems, List<RealtyBillWe> weBills) {
        if (CollectionUtils.isEmpty(weBills)) {
            return;
        }

        String lastRecordDate = DateUtils.formatYearMonth(
                weBills.stream().map(RealtyBillWe::getLastRecordDate).max(LocalDate::compareTo).orElse(null), DateUtils.PATTERN_FULL_DATE
        );
        String recordDate = DateUtils.formatYearMonth(
                weBills.stream().map(RealtyBillWe::getRecordDate).max(LocalDate::compareTo).orElse(null), DateUtils.PATTERN_FULL_DATE
        );
        // 水费
        RealtyBillItem waterBill = billItems.stream()
                .filter(x -> RealtyFee.WATER.getFeeId() == x.getFeeId())
                .findFirst()
                .orElse(null);
        if (waterBill == null) {
            waterBill = newRealtyFeeBill(RealtyFee.WATER);
            billItems.add(waterBill);
        }
        waterBill.setAttr1(lastRecordDate);
        waterBill.setAttr2(recordDate);
        waterBill.setAmount(weBills.stream().map(RealtyBillWe::getWaterAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

        // 电费
        RealtyBillItem electricBill = billItems.stream()
                .filter(x -> RealtyFee.ELECTRIC.getFeeId() == x.getFeeId())
                .findFirst()
                .orElse(null);
        if (electricBill == null) {
            electricBill = newRealtyFeeBill(RealtyFee.ELECTRIC);
            billItems.add(electricBill);
        }
        electricBill.setAttr1(lastRecordDate);
        electricBill.setAttr2(recordDate);
        electricBill.setAmount(weBills.stream().map(RealtyBillWe::getElectricAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    /**
     * 物业账单明细
     * @param fee
     * @return
     */
    private RealtyBillItem newRealtyFeeBill(RealtyFee fee) {
        RealtyBillItem result = new RealtyBillItem();
        result.setFeeId((long) fee.getFeeId());
        result.setFeeName(fee.getName());
        result.setAmount(BigDecimal.ZERO);
        return result;
    }


    /**
     * 应收账单合同列表
     * @param billDate
     * @return
     */
    private List<ContractVo> listRealtyBillContract(LocalDate billDate, String contractNo) {
        return listBillContract(billDate,
                contractNo,
                Arrays.asList(ContractType.LEASE.getValue(), ContractType.ESTATE.getValue()),
                false);
    }

    /**
     * 应付账单合同列表
     * @param billDate
     * @return
     */
    private List<ContractVo> listPayoffContract(LocalDate billDate, String contractNo) {
        return listBillContract(billDate,
                contractNo,
                Arrays.asList(ContractType.LEASEBACK.getValue(), ContractType.RENT_PROXY.getValue(), ContractType.RENT_COLLECTION_PROXY.getValue()),
                true);
    }

    /**
     * 待生成账单的合同列表
     * @param billDate 账单日
     * @param contractNo 合同号
     * @param types    合同类型
     * @param isPayoff 应付账单
     * @return
     */
    private List<ContractVo> listBillContract(LocalDate billDate, String contractNo, List<Integer> types, boolean isPayoff) {
        ContractBillSearchVo search = new ContractBillSearchVo();
        search.setContractNo(contractNo);
        search.setTypes(types);
        search.setStatus(ContractStatus.EFFECTIVE.ordinal());

        // 账单月第1天
        LocalDate monthFirstDay = DateUtils.parseDate(billDate.getYear(), billDate.getMonthValue(), 1);
        search.setEndDateBegin(monthFirstDay);
        // 账单月最后1天
        search.setStartDateEnd(billDate);

        search.setBillYear(billDate.getYear());
        search.setBillMonth(billDate.getMonthValue());

        if (isPayoff) {
            // 应付账单
            search.setStatelessEndDateBegin(monthFirstDay);
            search.setStatelessEndDateEnd(billDate);
            search.setPayoffGenerated(Boolean.FALSE);
        } else {
            // 应收账单
            search.setBillGenerated(Boolean.FALSE);
        }
        return contractService.listBillContract(search);
    }

    /**
     * 生成物业账单
     * @param contractNo
     * @param billDate
     */
    public void buildAndSaveBill(String contractNo, LocalDate billDate) {
        List<ContractVo> contractVos = listBillContract(billDate,
                contractNo,
                Arrays.asList(ContractType.LEASE.getValue(), ContractType.ESTATE.getValue()),
                true);
        if (CollectionUtils.isEmpty(contractVos)) {
            log.info("未找到生效的合同。。合同编号：{}, 日期：{}", contractNo, billDate);
            return;
        }
        buildAndSaveBill(contractVos.get(0), billDate, ContextUtils.getUserInContext());
    }

    /**
     * 生成物业账单
     * @param contractList
     * @param billDate
     */
    private void buildAndSaveBill(List<ContractVo> contractList, LocalDate billDate, AdminUserDto operator) {
        if (CollectionUtils.isEmpty(contractList)) {
            return;
        }

        // 租赁合同
        List<ContractVo> rentContracts = contractList.stream()
                .filter(x -> ContractType.fromValue(x.getType()) == ContractType.LEASE)
                .sorted(Comparator.nullsLast(Comparator.comparing(ContractVo::getEndDate)))
                .collect(Collectors.toList());

        LocalDate billTillDate = null;
        if (!CollectionUtils.isEmpty(rentContracts)) {
            // 根据租赁合同产生账单
            for (ContractVo contract : rentContracts) {
                log.info("【应收账单】根据物业 {} 租赁合同 {} 生成物业账单。", contract.getRealtySerial(), contract.getContractNo());
                buildAndSaveBill(contract, billDate, operator);
                billTillDate = DateUtils.getMinDate(contract.getEndDate(), billDate);
            }
        }

        // 根据租赁合同到期时间，未能覆盖完整个账单日
        if (billTillDate == null || billTillDate.compareTo(billDate) < 0) {
            Optional<ContractVo> op = contractList.stream().filter(x -> ContractType.fromValue(x.getType()) == ContractType.ESTATE).findFirst();
            if (!op.isPresent()) {
                log.info("【应收账单】物业 {} 无租赁合同或租赁合同 {} 到期。", contractList.get(0).getRealtySerial(), billTillDate);
                return;
            }

            // 根据物业合同生成账单
            ContractVo contract = op.get();
            // 有返租合同的，不产生应收
            if (isRealtyWithLeaseBackContract(contract.getRealtyId(), DateUtils.getFirstDateInMonth(billDate), billDate)) {
                log.info("【应收账单】物业 {} 无租赁合同，有物业合同 {} 及返租合同不生成账单。", contract.getRealtySerial()
                        , contract.getContractNo());
                return;
            }

            log.info("【应收账单】物业 {} 根据物业合同 {} 生成物业账单。", contract.getRealtySerial(), contract.getContractNo());
            if (billTillDate != null) {
                contract.setStartDate(billTillDate.plusDays(1L));
            }
            buildAndSaveBill(contract, billDate, operator);
        }
    }

    /**
     * 持久化应收账单
     * @param contract
     * @param billDate
     * @param operator
     */
    private void buildAndSaveBill(ContractVo contract, LocalDate billDate, AdminUserDto operator) {
        buildAndSaveBill(contract, billDate, operator, false);
    }

    /**
     * 持久化应收账单
     * @param contract
     * @param billDate
     * @param operator
     * @param isNewContract
     */
    private void buildAndSaveBill(ContractVo contract, LocalDate billDate, AdminUserDto operator, boolean isNewContract) {
        ContractType contractType = ContractType.fromValue(contract.getType());
        if (contractType != ContractType.LEASE && contractType != ContractType.ESTATE) {
            return;
        }

        billDate = billDate.compareTo(contract.getEndDate()) < 0 ? billDate : contract.getEndDate();
        // 账单信息初始化
        RealtyBill bill = buildContractBill(contract, billDate, operator);
        // 账单明细初始化
        List<RealtyBillItem> billItems = buildContractBillItems(contract, billDate);
        // 新合同启用，如果合同启用日期非当月，要把之前月份的管、租生成
        if (isNewContract) {
            while (!DateUtils.isSameYearMonth(contract.getStartDate(), billDate)) {
                billDate = DateUtils.getFirstDateInMonth(billDate).minusDays(1L);
                combineBillItems(billItems, buildContractBillItems(contract, billDate));
            }
        }

        if (billItems.stream().noneMatch(x -> x.getFeeId() == RealtyFee.WATER.getFeeId())) {
            billItems.add(newRealtyFeeBill(RealtyFee.WATER));
        }
        if (billItems.stream().noneMatch(x -> x.getFeeId() == RealtyFee.ELECTRIC.getFeeId())) {
            billItems.add(newRealtyFeeBill(RealtyFee.ELECTRIC));
        }

        realtyBillService.addBill(bill, billItems);
        penaltyService.calBillPenalty(contract.getContractNo());
    }

    /**
     * 停用合同账单
     * @param suspendDto
     */
    private void buildAndSaveSuspendBill(RealtyContractSuspendDto suspendDto, ContractVo contract, List<RealtyWe> weList) {
        // 是否自然到期
        boolean isNormalExpired = DateUtils.getDaysBetween(suspendDto.getSuspendDate(), contract.getEndDate()) == 0L;

        // 到期中止合同，并且水电结清
        if (CollectionUtils.isEmpty(weList) && isNormalExpired) {
            return;
        }

        // 停用合同处理应收账单
        boolean isBillPaid = suspendRealtyBill(suspendDto, isNormalExpired);

        // 水电账单
        log.info("【停用合同】停用合同 {}，生成水电账单 {}...", contract.getContractNo(), JsonUtils.object2Json(weList));
        List<RealtyBillWe> weBills = buildAndSaveWeBill(0L, contract, suspendDto.getSuspendDate(), weList);

        // 生成合同账单
        log.info("【停用合同】停用合同 {}，生成停用账单...", contract.getContractNo());
        // 防止滞纳金产生
        RealtyBill bill = buildContractBill(contract, suspendDto.getSuspendDate(), suspendDto.getOperator());
        List<RealtyBillItem> billItems = buildContractBillItems(contract, suspendDto.getSuspendDate(), isBillPaid || isNormalExpired);
        fixRealtyWeBill(billItems, weBills);
        realtyBillService.addBill(bill, billItems);

        // 计算滞纳金
        penaltyService.calBillPenalty(contract.getContractNo());
        // 回更账单id
        realtyBillWeService.updateWeBillId(weBills.stream().map(RealtyBillWe::getId).collect(Collectors.toList()), bill.getId());


        // 根据物业合同生成应收账单 有物业合同，无返租合同的物业，需要根据物业合同生成该月剩下日子的应收账单
        buildSuspendEstateBill(contract.getRealtyId(), suspendDto.getSuspendDate(), suspendDto.getOperator());
    }

    /**
     * 启用合同后，物业合同跟着调整(仅调整租金)
     * @param realtyId
     * @param enableDate
     */
    private void buildEnableEstateBill(Long realtyId, LocalDate enableDate, LocalDate billDate, AdminUserDto operator) {
        // 物业合同
        Contract contract = contractService.findRealtyContract(realtyId, ContractType.ESTATE.getValue());
        if (contract == null) {
            log.info("【启用合同】启用租赁合同，级联调整物业合同，找不到物业id {} 的物业合同，不需要处理。", realtyId);
            return;
        }

        RealtyBill bill = realtyBillService.findMonthlyBillByContractNo(contract.getContractNo(), enableDate.getYear(), enableDate.getMonthValue());
        if (bill == null) {
            log.info("【启用合同】启用租赁合同，级联调整物业合同，物业合同 {} - {} 账单不存在", contract.getContractNo(), DateUtils.formatYearMonth(enableDate));
            return;
        }
        BillStatus billStatus = BillStatus.fromStatus(bill.getStatus());
        if (BillStatus.PAID == billStatus) {
            log.info("【启用合同】启用租赁合同，级联调整物业合同，物业合同 {} - {} 账单已缴费，不做调整。", contract.getContractNo(), DateUtils.formatYearMonth(enableDate));
            return;
        }

        log.info("【启用合同】启用租赁合同，级联调整物业合同，物业合同 {} - {} 账单。",  contract.getContractNo(), DateUtils.formatYearMonth(enableDate));
        // 计算要减免的管理费
        List<ContractFee> manageFees = contractService.listContractFeeByFeeId(contract.getId(), RealtyFee.MANAGE.getFeeId());
        if (!CollectionUtils.isEmpty(manageFees)) {
            ContractFee manageFee = manageFees.stream()
                    .filter(x -> x.getCategory() == ContractFeeCategory.DEFAULT.ordinal())
                    .findFirst()
                    .orElse(new ContractFee(contract.getId(), (long) RealtyFee.MANAGE.getFeeId(), ContractFeeCategory.DEFAULT.ordinal(), BigDecimal.ZERO));
            List<ContractFee> periodFees = manageFees.stream()
                    .filter(x -> x.getCategory() == ContractFeeCategory.PERIOD.ordinal())
                    .collect(Collectors.toList());
            RealtyBillItem manageBill = buildContractBillItem(manageFee, periodFees, enableDate, billDate, false);

            // 物业合同的账单管理费扣减不需要缴纳的这部分管理费
            if (DecimalUtils.isPositive(manageBill.getAmount())) {
                BillFeeChangeVo feeChange = new BillFeeChangeVo(bill.getId(), (long) RealtyFee.MANAGE.getFeeId());
                feeChange.setChangeAmount(DecimalUtils.subtract(BigDecimal.ZERO, manageBill.getAmount()));
                feeChange.setOperatorId(operator.getUserId());
                feeChange.setOperatorName(operator.getUsername());
                realtyBillService.changeFeeBill(feeChange);

                // 账单金额
                BillMonthVo billMonth = BillMonthVo.builder()
                        .year(bill.getBillYear())
                        .month(bill.getBillMonth())
                        .billId(bill.getId())
                        .contractNo(bill.getContractNo())
                        .build();
                publisher.publishEvent(new RealtyBillChangedEvent(this, billMonth));
            }
        }

    }

    /**
     * 停用合同后，根据物业合同生成账单
     * @param realtyId
     * @param suspendDate
     * @param operator
     */
    private void buildSuspendEstateBill(Long realtyId, LocalDate suspendDate, AdminUserDto operator) {
        // 物业合同
        Contract contract = contractService.findRealtyContract(realtyId, ContractType.ESTATE.getValue());
        if (contract == null) {
            log.info("【停用合同】找不到物业合同， 物业id {}", realtyId);
            return;
        }

        // 物业合同带返租合同
        if (isRealtyWithLeaseBackContract(contract.getRealtyId(), suspendDate, suspendDate)) {
            log.info("【停用合同】物业合同带有返祖合同， 物业id {}，时间 {}", realtyId, suspendDate);
            return;
        }

        // 计费时间从停止合同下一刻开始
        LocalDate billStartDate = suspendDate.plusDays(1L);
        if (billStartDate.getMonthValue() != suspendDate.getMonthValue()) {
            log.info("【停用合同】日期 {} 为该月最后一天，无需根据物业合同生成应收账单。", suspendDate);
            return;
        }

        ContractVo contractVo = contractConvertor.toVo(contract);
        contractVo.setStartDate(billStartDate);

        // 生成账单
        LocalDate billEndDate = DateUtils.getLastDateInMonth(billStartDate);
        RealtyBill bill = buildContractBill(contractVo, billEndDate, operator);
        List<RealtyBillItem> estateBillItems = buildContractBillItems(contractVo, billEndDate);
        realtyBillService.addBill(bill, estateBillItems);
        // 计算滞纳金
        penaltyService.calBillPenalty(contractVo.getContractNo());
    }

    /**
     * 持久化应付账单
     * @param contract
     * @param billDate
     * @param operator
     */
    private void buildAndSavePayoff(ContractVo contract, LocalDate billDate, AdminUserDto operator) {
        buildAndSavePayoff(contract, billDate, operator, false);
    }

    /**
     * 生成应付账单
     * @param contract
     * @param billDate
     * @param operator
     * @param isNewContract
     */
    private void buildAndSavePayoff(ContractVo contract, LocalDate billDate, AdminUserDto operator, boolean isNewContract) {
        ContractType contractType = ContractType.fromValue(contract.getType());
        if (contractType != ContractType.LEASEBACK
                && contractType != ContractType.RENT_PROXY
                && contractType != ContractType.RENT_COLLECTION_PROXY) {
            return;
        }

        // 账单信息初始化
        RealtyPayoff payoff = buildPayoff(contract, billDate, operator);
        // 账单明细初始化
        List<RealtyPayoffItem> items = buildPayoffItems(contract, billDate);
        if (isNewContract) {
            while (!DateUtils.isSameYearMonth(contract.getStartDate(), billDate)) {
                billDate = DateUtils.getFirstDateInMonth(billDate).minusDays(1L);
                combinePayoffItems(items, buildPayoffItems(contract, billDate));
            }
        }

        if (!CollectionUtils.isEmpty(items)) {
            payoffService.addPayoff(payoff, items);
        }
    }

    /**
     * 停用合同，产生应付账单
     * @param contract
     * @param suspendDto
     */
    private void buildAndSaveSuspendPayoff(ContractVo contract, RealtyContractSuspendDto suspendDto) {
        BillMonthVo billMonth = new BillMonthVo(suspendDto.getSuspendDate().getYear(), suspendDto.getSuspendDate().getMonthValue());
        // 当前月份是否已经产生了应付账单
        List<RealtyPayoff> payoffList = payoffService.findByContractNo(billMonth.getYear(), billMonth.getMonth(), contract.getContractNo());

        if (CollectionUtils.isEmpty(payoffList)) {
            buildAndSavePayoff(contract, suspendDto.getSuspendDate(), suspendDto.getOperator());

        } else {
            Optional<RealtyPayoff> op = payoffList.stream().filter(x -> x.getStatus() == BillStatus.INIT.getStatus()).findFirst();
            if (op.isPresent()) {
                log.info("【停用合同】合同 {} {}的应付账单易产生，更新账单金额。", suspendDto.getContractNo(),
                        StringUtils.buildYearMonthStr(billMonth.getYear(), billMonth.getMonth()));
                // 账单信息初始化
                RealtyPayoff payoff = buildPayoff(contract, suspendDto.getSuspendDate(), suspendDto.getOperator());
                payoff.setId(op.get().getId());
                // 账单明细初始化
                List<RealtyPayoffItem> items = buildPayoffItems(contract, suspendDto.getSuspendDate());
                payoffService.updatePayoff(payoff, items);
            } else {
                log.info("【停用合同】合同 {} {}的应付账单易产生，停用合同不做变更。", suspendDto.getContractNo(),
                        StringUtils.buildYearMonthStr(billMonth.getYear(), billMonth.getMonth()));
            }
        }
    }

    /**
     * 根据物业账单时间计算水电账单时间
     *
     * @param year
     * @param month
     * @return
     */
    private BillMonthVo calRealtyWeTime(Integer year, Integer month) {
        month--;
        if (month < Month.JANUARY.getValue()) {
            year--;
            month = Month.DECEMBER.getValue();
        }
        return new BillMonthVo(year, month);
    }

    /**
     * 返祖合同是否存在
     * @param realtyId
     * @param billStartDate
     * @param billEndDate
     * @return
     */
    private boolean isRealtyWithLeaseBackContract(Long realtyId, LocalDate billStartDate, LocalDate billEndDate) {
        if (!WrapperClassUtils.biggerThanLong(realtyId, 0L)) {
            return false;
        }

        ContractSearchVo search = new ContractSearchVo();
        search.setRealtyId(realtyId);
        search.setType(ContractType.LEASEBACK.getValue());
        search.setStartDateEnd(billEndDate);
        search.setEndDateBegin(billStartDate);
        return contractService.countContract(search) > 0;
    }

    /**
     * 合并应收账单明细
     * @param list
     * @param addList
     * @return
     */
    private void combineBillItems(List<RealtyBillItem> list, List<RealtyBillItem> addList) {
        if (CollectionUtils.isEmpty(addList)) {
            return;
        }

        for (RealtyBillItem item : addList) {
            Optional<RealtyBillItem> op = list.stream().filter(x -> Objects.equals(x.getFeeId(), item.getFeeId())).findFirst();
            if (op.isPresent()) {
                RealtyBillItem opItem = op.get();
                opItem.setAmount(DecimalUtils.add(opItem.getAmount(), item.getAmount()));
            } else {
                list.add(item);
            }
        }
    }

    /**
     * 合并应付账单明细
     * @param list
     * @param addList
     */
    private void combinePayoffItems(List<RealtyPayoffItem> list, List<RealtyPayoffItem> addList) {
        if (CollectionUtils.isEmpty(addList)) {
            return;
        }

        for (RealtyPayoffItem item : addList) {
            Optional<RealtyPayoffItem> op = list.stream().filter(x -> Objects.equals(x.getFeeId(), item.getFeeId())).findFirst();
            if (op.isPresent()) {
                RealtyPayoffItem opItem = op.get();
                opItem.setAmount(DecimalUtils.add(opItem.getAmount(), item.getAmount()));
            } else {
                list.add(item);
            }
        }
    }

    /**
     * 停用合同处理应收账单
     * @param suspendDto
     * @return 管、租费用是否已缴
     */
    private boolean suspendRealtyBill(RealtyContractSuspendDto suspendDto, boolean isNormalExpired) {
        // 如果本月该合同存在未缴月账单，将其物业及管理费置0
        RealtyBill monthlyBill = realtyBillService.findMonthlyBillByContractNo(suspendDto.getContractNo(), suspendDto.getYear(), suspendDto.getMonth());
        boolean isContractBillPaid = monthlyBill != null && BillStatus.fromStatus(monthlyBill.getStatus()) == BillStatus.PAID;

        if (!isNormalExpired) {
            // 停用月份的账单修改
            if (!isContractBillPaid) {
                realtyBillService.suspendRealtyBill(suspendDto);
            }

            // 账单月份的账单修改
            // 停用合同时间非账单月，需要处理账单月的应收账单，将物业费及管理费置0
            LocalDate systemBillDate = findBillTimeFromSystemSetting();
            if (systemBillDate != null && !DateUtils.isSameYearMonth(systemBillDate, suspendDto.getSuspendDate())) {
                RealtyBill bill = realtyBillService.findMonthlyBillByContractNo(suspendDto.getContractNo(), systemBillDate.getYear(), systemBillDate.getMonthValue());
                boolean isBillPaid = monthlyBill == null || bill == null || BillStatus.fromStatus(bill.getStatus()) == BillStatus.PAID;
                if (!isBillPaid) {
                    RealtyContractSuspendDto billSuspend = new RealtyContractSuspendDto();
                    billSuspend.setContractNo(suspendDto.getContractNo());
                    billSuspend.setYear(systemBillDate.getYear());
                    billSuspend.setMonth(systemBillDate.getMonthValue());
                    billSuspend.setOperator(suspendDto.getOperator());
                    realtyBillService.suspendRealtyBill(billSuspend);
                }
            }
        }

        return isContractBillPaid;
    }

    private LocalDate findBillTimeFromSystemSetting() {
        SystemSetting yearSetting = settingService.findByParam(SystemParam.REALTY_BILL_YEAR);
        SystemSetting monthSetting = settingService.findByParam(SystemParam.REALTY_BILL_MONTH);

        try {
            if (yearSetting != null && monthSetting != null) {
                return DateUtils.getLastDateInMonth(LocalDate.of(Integer.parseInt(yearSetting.getValue()), Integer.parseInt(monthSetting.getValue()), 1));
            }
        } catch (Exception e) {
            log.warn("系统参数账单年月错误 {} {}", yearSetting.getValue(), monthSetting.getValue());
        }
        return null;
    }

    private void prepareEnergyPrice(ContractVo contract, EnergyType energyType) {
        switch (energyType) {
            case WATER:
                if (contract.getWaterPrice() == null && contract.getWaterPriceType() != null) {
                    WaterElectricPriceType waterPrice = waterElectricPriceTypeService.findById(contract.getWaterPriceType().longValue());
                    contract.setWaterPrice(waterElectricPriceTypeConvertor.toV(waterPrice));
                }
                break;
            case ELECTRIC:
                if (contract.getElectricPrice() == null && contract.getElectricPriceType() != null) {
                    WaterElectricPriceType electricPrice = waterElectricPriceTypeService.findById(contract.getElectricPriceType().longValue());
                    contract.setElectricPrice(waterElectricPriceTypeConvertor.toV(electricPrice));
                }
                break;
            default:
                break;
        }
    }

    private void checkContractEnergyPrice(ContractVo contract) {
        if (!WrapperClassUtils.biggerThanInt(contract.getElectricPriceType(), 0)
                || !WrapperClassUtils.biggerThanInt(contract.getWaterPriceType(), 0)) {
            throw new BusinessException("请先设置合同水电类型");
        }
    }

}

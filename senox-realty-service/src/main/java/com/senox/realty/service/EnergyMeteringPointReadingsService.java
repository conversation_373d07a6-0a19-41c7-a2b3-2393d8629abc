package com.senox.realty.service;


import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import com.senox.common.constant.device.EnergyType;
import com.senox.dm.vo.HolleyPointMeterQueryBatchRequest;
import com.senox.realty.component.DeviceEnergyMeteringPointReadingsComponent;
import com.senox.realty.constant.RealtyConst;
import com.senox.realty.domain.EnergyMeteringPointReadings;
import com.senox.realty.mapper.EnergyMeteringPointReadingsMapper;
import com.senox.realty.utils.ContextUtils;
import com.senox.realty.vo.EnergyMeteringPointReadingsSearchVo;
import com.senox.realty.vo.RealtyToEnergyMeteringPointReadingsSearchVo;
import com.senox.realty.vo.RealtyToEnergyMeteringPointVo;
import com.xxl.job.core.context.XxlJobHelper;
import io.foldright.cffu.Cffu;
import io.foldright.cffu.CffuFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-11-08
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class EnergyMeteringPointReadingsService {
    private final EnergyMeteringPointReadingsMapper meteringPointReadingsMapper;
    private final RealtyEnergyMeteringPointService realtyEnergyMeteringPointService;
    private final DeviceEnergyMeteringPointReadingsComponent meteringPointReadingsComponent;
    private final CffuFactory cffuFactory;

    /**
     * 读数列表
     * @param batchReq 请求参数
     * @return 返回读数列表
     */
    public List<EnergyMeteringPointReadings> readingsList(EnergyType energyType, HolleyPointMeterQueryBatchRequest batchReq) {
        List<EnergyMeteringPointReadings> meteringPointReadingsList = meteringPointReadingsComponent.remoteList(energyType, batchReq);
        if (CollectionUtils.isEmpty(meteringPointReadingsList)) {
            return Collections.emptyList();
        }
        List<String> meteringPointCodes = meteringPointReadingsList.stream().map(EnergyMeteringPointReadings::getPointCode).collect(Collectors.toList());
        List<RealtyToEnergyMeteringPointVo> meteringPoints = realtyEnergyMeteringPointService.meteringPointToRealtyListByCode(meteringPointCodes);
        Map<String, RealtyToEnergyMeteringPointVo> meteringPointMap = meteringPoints.stream().collect(Collectors.toMap(RealtyToEnergyMeteringPointVo::getCode, Function.identity()));
        for (EnergyMeteringPointReadings meteringPointReadings : meteringPointReadingsList) {
            RealtyToEnergyMeteringPointVo meteringPoint = meteringPointMap.get(meteringPointReadings.getPointCode());
            if (null == meteringPoint) {
                continue;
            }
            meteringPointReadings.setPointRate(meteringPoint.getRate());
            meteringPointReadings.setPointType(meteringPoint.getEnergyType());
            meteringPointReadings.setRealtySerialNo(meteringPoint.getRealtySerialNo());
            meteringPointReadings.setRealtyName(meteringPoint.getRealtyName());
        }
        return meteringPointReadingsList;
    }

    /**
     * 水读数列表
     * @param batchReq 请求参数
     * @return 返回水读数列表
     */
    public List<EnergyMeteringPointReadings> waterReadingsList(HolleyPointMeterQueryBatchRequest batchReq) {
        return readingsList(EnergyType.WATER, batchReq);
    }

    /**
     * 电读数列表
     * @param batchReq 请求参数
     * @return 返回电读数列表
     */
    public List<EnergyMeteringPointReadings> electricReadingsList(HolleyPointMeterQueryBatchRequest batchReq) {
        return readingsList(EnergyType.ELECTRIC, batchReq);
    }

    /**
     * 批量添加计量点读数
     * @param meteringPointReadingsList 计量点读数集
     */
    public void addBatch(List<EnergyMeteringPointReadings> meteringPointReadingsList, boolean isNew) {
        if (CollectionUtils.isEmpty(meteringPointReadingsList)) {
            return;
        }
        meteringPointReadingsList.forEach(r -> {
            ContextUtils.initEntityCreator(r);
            ContextUtils.initEntityModifier(r);
            r.setCreateTime(LocalDateTime.now());
            r.setModifiedTime(LocalDateTime.now());
        });
        meteringPointReadingsMapper.addBatch(meteringPointReadingsList, isNew);
    }

    /**
     * 批量修改计量点读数
     * @param meteringPointReadingsList 计量点读数集
     */
    public void updateBatch(List<EnergyMeteringPointReadings> meteringPointReadingsList, boolean isNew) {
        updateBatch(meteringPointReadingsList, isNew, AdminContext.getUser());
    }

    /**
     * 批量修改计量点读数
     * @param meteringPointReadingsList 计量点读数集
     * @param adminUser 用户
     */
    public void updateBatch(List<EnergyMeteringPointReadings> meteringPointReadingsList, boolean isNew, AdminUserDto adminUser) {
        if (CollectionUtils.isEmpty(meteringPointReadingsList) || null == adminUser) {
            return;
        }
        meteringPointReadingsList.forEach(r -> {
            r.setModifierId(adminUser.getUserId());
            r.setModifierName(adminUser.getUsername());
            r.setModifiedTime(LocalDateTime.now());
        });
        meteringPointReadingsMapper.updateBatch(meteringPointReadingsList, isNew);
    }

    /**
     * 列表统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(EnergyMeteringPointReadingsSearchVo search) {
        return meteringPointReadingsMapper.countList(search);
    }

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的数据
     */
    public List<EnergyMeteringPointReadings> list(EnergyMeteringPointReadingsSearchVo search) {
        if (null == search) {
            search = new RealtyToEnergyMeteringPointReadingsSearchVo();
            search.setPage(false);
        }
        return meteringPointReadingsMapper.list(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<EnergyMeteringPointReadings> pageList(EnergyMeteringPointReadingsSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search, () -> countList(search), () -> list(search));
    }

    /**
     * 刷新最新读数数据
     */
    public void refreshMeteringPointReadingsNew(List<EnergyMeteringPointReadings> meteringPointReadingsList) {
        if (CollectionUtils.isEmpty(meteringPointReadingsList)) {
            return;
        }
        //获取最新的读数
        Map<String, EnergyMeteringPointReadings> meteringPointReadingsMap = new HashMap<>();
        meteringPointReadingsList.forEach(readings -> {
            EnergyMeteringPointReadings readings2 = meteringPointReadingsMap.get(readings.getPointCode());
            if (null != readings2) {
                if (readings2.getDataTime().isBefore(readings.getDataTime())) {
                    meteringPointReadingsMap.put(readings.getPointCode(), readings);
                }
                return;
            }
            meteringPointReadingsMap.put(readings.getPointCode(), readings);
        });
        EnergyMeteringPointReadingsSearchVo search = new EnergyMeteringPointReadingsSearchVo();
        search.setRealTime(true);
        search.setPage(false);
        List<EnergyMeteringPointReadings> dbMeteringPointReadingsList = list(search);
        DataSepDto<EnergyMeteringPointReadings> dataSepDto = compareReadings(dbMeteringPointReadingsList, new ArrayList<>(meteringPointReadingsMap.values()));
        if (!CollectionUtils.isEmpty(dataSepDto.getAddList())) {
            addBatch(dataSepDto.getAddList(), true);
        }
        if (!CollectionUtils.isEmpty(dataSepDto.getUpdateList())) {
            updateBatch(dataSepDto.getUpdateList(), true);
        }
    }

    /**
     * 批量同步水电表读数
     *
     * @param batchReq 请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSync(HolleyPointMeterQueryBatchRequest batchReq) {
        List<EnergyMeteringPointReadings> meteringPointReadingsList = new ArrayList<>();
        meteringPointReadingsList.addAll(waterReadingsList(batchReq));
        meteringPointReadingsList.addAll(electricReadingsList(batchReq));
        saveBatch(meteringPointReadingsList, false, true);
    }

    /**
     * 批量插入读数
     * @param meteringPointReadingsList 读数列表
     * @param isBatch 是否批量
     * @param refreshNew 刷新最新读数
     */
    public void saveBatch(List<EnergyMeteringPointReadings> meteringPointReadingsList, boolean isBatch, boolean refreshNew) {
        if (CollectionUtils.isEmpty(meteringPointReadingsList)) {
            return;
        }
        //刷新最新读数数据
        if (refreshNew) {
            refreshMeteringPointReadingsNew(meteringPointReadingsList);
        }
        if (isBatch) {
            addBatch(meteringPointReadingsList, false);
        } else {
            for (EnergyMeteringPointReadings meteringPointReadings : meteringPointReadingsList) {
                try {
                    addBatch(Collections.singletonList(meteringPointReadings), false);
                } catch (DuplicateKeyException duplicateKeyException) {
                    String logPattern = String.format("数据重复 终端:%s,设备:%s,时间:%s", meteringPointReadings.getRtuCode(), meteringPointReadings.getPointCode(), DateUtils.formatDateTime(meteringPointReadings.getDataTime(), DateUtils.PATTERN_FULL_DATE_TIME));
                    if (WrapperClassUtils.biggerThanLong(XxlJobHelper.getJobId(), 0)) {
                        XxlJobHelper.log(logPattern);
                    } else {
                        log.error(logPattern);
                    }
                }
            }
        }
    }

    /**
     * 根据计量点编码查找读数
     * @param meteringPointCode 计量点编码
     * @param isNew 是否最新
     * @return 返回查找到的读数
     */
    public List<EnergyMeteringPointReadings> findByMeteringPointCode(String meteringPointCode, boolean isNew) {
        if (StringUtils.isBlank(meteringPointCode)) {
            return Collections.emptyList();
        }
        EnergyMeteringPointReadingsSearchVo search = new EnergyMeteringPointReadingsSearchVo();
        search.setPointCode(meteringPointCode);
        search.setPage(false);
        search.setRealTime(isNew);
        return list(search);
    }

    /**
     * 更新计量点编码
     * @param oldMeteringPointCode 原计量点编码
     * @param newMeteringPointCode 新计量点编码
     * @param realtySerialNo 物业编号
     * @param realtyName 物业名称
     */
    public void updateMeteringPointCode(String oldMeteringPointCode, String newMeteringPointCode, String realtySerialNo, String realtyName) {
        if (StringUtils.isBlank(oldMeteringPointCode) || StringUtils.isBlank(newMeteringPointCode)) {
            return;
        }
        List<EnergyMeteringPointReadings> meteringPointReadingsList = findByMeteringPointCode(oldMeteringPointCode, false);
        List<EnergyMeteringPointReadings> newMeteringPointReadingsList = findByMeteringPointCode(oldMeteringPointCode, true);
        if (CollectionUtils.isEmpty(meteringPointReadingsList) && CollectionUtils.isEmpty(newMeteringPointReadingsList)) {
            return;
        }
        List<EnergyMeteringPointReadings> updateMeteringPointReadingList = updateReadingsMeteringPointCodeForeach(meteringPointReadingsList, newMeteringPointCode, realtySerialNo, realtyName);
        List<EnergyMeteringPointReadings> updateNewMeteringPointReadingList = updateReadingsMeteringPointCodeForeach(newMeteringPointReadingsList, newMeteringPointCode, realtySerialNo, realtyName);
        List<CompletableFuture<Void>> futures = new ArrayList<>(updateMeteringPointReadingList.size() / RealtyConst.BATCH_SIZE_1000 + 1);
        AdminUserDto adminUser = AdminContext.getUser();
        //多线程处理，每个线程处理1000条
        for (int i = 0; i < updateMeteringPointReadingList.size(); i += RealtyConst.BATCH_SIZE_1000) {
            List<EnergyMeteringPointReadings> splitList = updateMeteringPointReadingList.stream().skip(i).limit(RealtyConst.BATCH_SIZE_1000).collect(Collectors.toList());
            futures.add(i, CompletableFuture.runAsync(() -> updateBatch(splitList, false, adminUser)));
        }
        updateBatch(updateNewMeteringPointReadingList, true, adminUser);
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 计量点读数同步物业信息
     * @param search 查询参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void energyMeteringPointReadingRealtyInfoSync(EnergyMeteringPointReadingsSearchVo search) {
        if (null == search) {
            search = new EnergyMeteringPointReadingsSearchVo();
            search.setPage(false);
            search.setRealTime(false);
        } else {
            search.setPage(false);
        }
        List<EnergyMeteringPointReadings> meteringPointReadingsList = list(search);
        List<String> meteringPointCodes = meteringPointReadingsList.stream().map(EnergyMeteringPointReadings::getPointCode).collect(Collectors.toList());
        List<RealtyToEnergyMeteringPointVo> meteringPoints = realtyEnergyMeteringPointService.meteringPointToRealtyListByCode(meteringPointCodes);
        Map<String, RealtyToEnergyMeteringPointVo> meteringPointMap = meteringPoints.stream().collect(Collectors.toMap(RealtyToEnergyMeteringPointVo::getCode, Function.identity()));
        List<EnergyMeteringPointReadings> updateMeteringPointReadingsList = new ArrayList<>(meteringPointReadingsList.size());
        for (EnergyMeteringPointReadings meteringPointReadings : meteringPointReadingsList) {
            RealtyToEnergyMeteringPointVo meteringPoint = meteringPointMap.get(meteringPointReadings.getPointCode());
            if (null == meteringPoint) {
                continue;
            }
            EnergyMeteringPointReadings updateMeteringPointReadings = new EnergyMeteringPointReadings();
            updateMeteringPointReadings.setId(meteringPointReadings.getId());
            updateMeteringPointReadings.setPointRate(meteringPoint.getRate());
            updateMeteringPointReadings.setPointType(meteringPoint.getEnergyType());
            updateMeteringPointReadings.setRealtySerialNo(meteringPoint.getRealtySerialNo());
            updateMeteringPointReadings.setRealtyName(meteringPoint.getRealtyName());
            updateMeteringPointReadingsList.add(updateMeteringPointReadings);
        }
        List<Cffu<Void>> futures = new ArrayList<>(updateMeteringPointReadingsList.size() / RealtyConst.BATCH_SIZE_1000 + 1);
        final Boolean realTime = search.getRealTime();
        //多线程处理，每个线程处理1000条
        for (int i = 0; i < updateMeteringPointReadingsList.size(); i += RealtyConst.BATCH_SIZE_1000) {
            List<EnergyMeteringPointReadings> splitList = updateMeteringPointReadingsList.stream().skip(i).limit(RealtyConst.BATCH_SIZE_1000).collect(Collectors.toList());
            futures.add(cffuFactory.runAsync(() -> updateBatch(splitList, realTime)));
        }
        cffuFactory.allOf(futures.toArray(new Cffu[0])).join();
    }

    /**
     * 循环更新计量点读数编码
     * @param meteringPointReadingList 计量点读数集
     * @param newMeteringPointCode 新的计量点编号
     * @param realtySerialNo 物业编号
     * @param realtyName 物业名称
     * @return 返回读数集
     */
    private List<EnergyMeteringPointReadings> updateReadingsMeteringPointCodeForeach(List<EnergyMeteringPointReadings> meteringPointReadingList, String newMeteringPointCode, String realtySerialNo, String realtyName) {
        if (CollectionUtils.isEmpty(meteringPointReadingList)) {
            return Collections.emptyList();
        }
        List<EnergyMeteringPointReadings> updateMeteringPointReadingList = new ArrayList<>(meteringPointReadingList.size());
        for (EnergyMeteringPointReadings meteringPointReadings : meteringPointReadingList) {
            EnergyMeteringPointReadings updateMeteringPointReadings = prepareNewEnergyMeteringPointReadings(meteringPointReadings, newMeteringPointCode, realtySerialNo, realtyName);
            if (null == updateMeteringPointReadings) {
                continue;
            }
            updateMeteringPointReadingList.add(updateMeteringPointReadings);
        }
        return updateMeteringPointReadingList;
    }

    /**
     * 生成新的计量点读数
     * @param oldMeteringPointReadings 旧的计量点读数
     * @param newMeteringPointCode 新的计量点编号
     * @param realtySerialNo 物业编号
     * @return 返回新的计量点读数
     */
    private static EnergyMeteringPointReadings prepareNewEnergyMeteringPointReadings(EnergyMeteringPointReadings oldMeteringPointReadings, String newMeteringPointCode, String realtySerialNo, String realtyName) {
        EnergyMeteringPointReadings newMeteringPointReadings = new EnergyMeteringPointReadings();
        newMeteringPointReadings.setId(oldMeteringPointReadings.getId());
        if (null != newMeteringPointCode && !oldMeteringPointReadings.getPointCode().equals(newMeteringPointCode)) {
            newMeteringPointReadings.setPointCode(newMeteringPointCode);
        }
        if (null != realtySerialNo && (StringUtils.isBlank(oldMeteringPointReadings.getRealtySerialNo()) || !oldMeteringPointReadings.getRealtySerialNo().equals(realtySerialNo))) {
            newMeteringPointReadings.setRealtySerialNo(realtySerialNo);
        }
        if (null != realtyName && (StringUtils.isBlank(oldMeteringPointReadings.getRealtyName()) || !oldMeteringPointReadings.getRealtyName().equals(realtyName))) {
            newMeteringPointReadings.setRealtyName(realtyName);
        }
        return (StringUtils.isBlank(newMeteringPointReadings.getPointCode())
                && StringUtils.isBlank(newMeteringPointReadings.getRealtySerialNo())
                && StringUtils.isBlank(newMeteringPointReadings.getRealtyName())) ? null : newMeteringPointReadings;
    }

    /**
     * 比较计量点读数
     * @param srcList 源集
     * @param targetList 目标集
     * @return 返回比较结果
     */
    private DataSepDto<EnergyMeteringPointReadings> compareReadings(List<EnergyMeteringPointReadings> srcList, List<EnergyMeteringPointReadings> targetList) {
        if (CollectionUtils.isEmpty(targetList)) {
            return new DataSepDto<>();
        }
        List<EnergyMeteringPointReadings> addList = new ArrayList<>(targetList.size());
        List<EnergyMeteringPointReadings> updateList = new ArrayList<>(srcList.size());
        if (CollectionUtils.isEmpty(srcList)) {
            addList.addAll(targetList);
        } else {
            Map<String, EnergyMeteringPointReadings> srcMap = srcList.stream().collect(Collectors.toMap(EnergyMeteringPointReadings::getPointCode, Function.identity()));
            targetList.forEach(t -> {
                EnergyMeteringPointReadings pointMeter = srcMap.get(t.getPointCode());
                if (null != pointMeter) {
                    if (pointMeter.getDataTime().isBefore(t.getDataTime())) {
                        pointMeter.setReadings(t.getReadings());
                        pointMeter.setDataTime(t.getDataTime());
                        pointMeter.setGrabTime(LocalDateTime.now());
                        updateList.add(pointMeter);
                    }
                } else {
                    t.setGrabTime(LocalDateTime.now());
                    addList.add(t);
                }
            });
        }
        return new DataSepDto<>(addList, updateList, null);
    }
}

package com.senox.realty.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.domain.Realty;
import com.senox.realty.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-11
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class EnergyMeteringPointBusinessService {
    private final RealtyEnergyMeteringPointService realtyEnergyMeteringPointService;
    private final EnergyMeteringPointReadingsService energyMeteringPointReadingsService;
    private final RealtyService realtyService;
    private final EnergyMeteringPointService meteringPointService;

    /**
     * 物业绑定计量设备
     *
     * @param realtyBindPoint 绑定参数
     * @param checkBind       检查绑定
     * @return 返回true表示绑定成功
     */
    public boolean meteringPointBindRealty(RealtyBindEnergyMeteringPointVo realtyBindPoint, boolean checkBind) {
        if (StringUtils.isBlank(realtyBindPoint.getPointCode())) {
            return false;
        }
        if (StringUtils.isBlank(realtyBindPoint.getRealtySerialNo())) {
            //解绑
            if (!realtyEnergyMeteringPointService.unBindPointDevice(realtyBindPoint.getPointCode())) {
                throw new BusinessException("设备解绑失败！");
            }
            return false;
        }

        if (null == realtyBindPoint.getEnergyType()) {
            RealtyToEnergyMeteringPointVo meteringPoint = realtyEnergyMeteringPointService.meteringPointToRealtyFindByPointCode(realtyBindPoint.getPointCode());
            if (null == meteringPoint) {
                throw new BusinessException("未找到设备");
            }
            realtyBindPoint.setEnergyType(EnergyType.fromValue(meteringPoint.getEnergyType()));
        }
        int count = realtyEnergyMeteringPointService.checkPointDeviceBind(realtyBindPoint, realtyBindPoint.getEnergyType().getValue());
        if (count > 0) {
            if (checkBind) {
                throw new BusinessException("物业或计量设备被占用");
            }
            return false;
        }
        //计量点绑定物业
        realtyEnergyMeteringPointService.bindPointDevice(realtyBindPoint, realtyBindPoint.getEnergyType().getValue());
        return true;
    }

    /**
     * 物业绑定计量设备
     *
     * @param realtyBindPoints 绑定参数集
     * @return 返回绑定数
     */
    public int meteringPointBindRealty(List<RealtyBindEnergyMeteringPointVo> realtyBindPoints) {
        if (CollectionUtils.isEmpty(realtyBindPoints)) {
            return 0;
        }
        int count = 0;
        for (RealtyBindEnergyMeteringPointVo realtyBindPoint : realtyBindPoints) {
            if (meteringPointBindRealty(realtyBindPoint, false)) {
                count++;
            }
        }
        return count;
    }

    /**
     * 自动绑定计量设备
     *
     * @param search 查询参数
     * @return 返回处理结果
     */
    public PointBindRealtyResult automaticMeteringPointBindRealty(RealtyToEnergyMeteringPointSearchVo search) {
        search.setPage(false);
        search.setExistRealty(false);
        List<RealtyToEnergyMeteringPointVo> meteringPoints = realtyEnergyMeteringPointService.meteringPointToRealtyList(search);
        RealtySearchVo realtySearch = new RealtySearchVo();
        realtySearch.setPage(false);
        Map<String, List<Realty>> regionRealtyMap = realtyService.listRealty(realtySearch).stream().collect(Collectors.groupingBy(Realty::getRegionName));
        String regex = "[-－]";
        PointBindRealtyResult result = new PointBindRealtyResult();
        List<RealtyBindEnergyMeteringPointVo> realtyBindPoints = new ArrayList<>(meteringPoints.size());
        for (RealtyToEnergyMeteringPointVo meteringPoint : meteringPoints) {
            String[] split = meteringPoint.getName().split(regex);
            if (split.length < 1) {
                log.error("数组长度校验不通过: {}", JsonUtils.object2Json(meteringPoint));
                result.setFailCount(result.getFailCount() + 1);
            }
            String regionName = split[0];
            String realtyName = split[1];
            //替换名称
            realtyName = replaceName(realtyName);
            //获取区域物业
            List<Realty> realtyList = regionRealtyMap.get(regionName);
            if (CollectionUtils.isEmpty(realtyList)) {
                log.warn("区域物业数据为空: {} - {} - {}", meteringPoint.getCode(), meteringPoint.getName(), regionName);
                result.setFailCount(result.getFailCount() + 1);
                continue;
            }
            boolean isBind = false;
            for (Realty realty : realtyList) {
                if (regionName.concat(realtyName).matches(Pattern.quote(realty.getName()).concat(".*"))) {
                    RealtyBindEnergyMeteringPointVo realtyBindPoint = new RealtyBindEnergyMeteringPointVo();
                    realtyBindPoint.setPointCode(meteringPoint.getCode());
                    realtyBindPoint.setRealtySerialNo(realty.getSerialNo());
                    realtyBindPoint.setRealtyName(realty.getName());
                    realtyBindPoint.setEnergyType(EnergyType.fromValue(meteringPoint.getEnergyType()));
                    realtyBindPoints.add(realtyBindPoint);
                    isBind = true;
                }
            }
            if (!isBind) {
                result.setFailCount(result.getFailCount() + 1);
                log.info("未匹配: {} - {} - {}", meteringPoint.getCode(), meteringPoint.getName(), regionName.concat(realtyName));
            }
        }
        result.setSuccessCount(meteringPointBindRealty(realtyBindPoints));
        result.setTotalCount(result.getFailCount() + result.getSuccessCount());
        return result;
    }

    /**
     * 计量点作废
     * @param meteringPointCode 计量点编码
     */
    public void meteringPointCancel(String meteringPointCode) {
        if (StringUtils.isBlank(meteringPointCode)) {
            return;
        }
        RealtyToEnergyMeteringPointVo meteringPoint = realtyEnergyMeteringPointService.meteringPointToRealtyFindByPointCode(meteringPointCode);
        if (null != meteringPoint && !StringUtils.isBlank(meteringPoint.getRealtySerialNo())) {
            //跟物业解绑
            realtyEnergyMeteringPointService.unBindPointDevice(meteringPointCode);
        }
        //逻辑删除计量点
        meteringPointService.deleteByCode(meteringPointCode);
    }

    /**
     * 替换名称
     *
     * @param realtyName 物业名
     * @return 返回替换后的名称
     */
    private String replaceName(String realtyName) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(realtyName);
        if (matcher.find()) {
            String number = matcher.group();
            String newNumber = StringUtils.fixLength(number, 3, '0');
            return realtyName.replace(number, newNumber);
        }
        return realtyName;
    }
}

package com.senox.realty.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.realty.constant.InspectResult;
import com.senox.realty.constant.InspectionType;
import com.senox.realty.domain.FirefightingStoreInspection;
import com.senox.realty.event.InspectTaskDropEvent;
import com.senox.realty.event.InspectTaskFulfilledEvent;
import com.senox.realty.mapper.FirefightingStoreInspectionMapper;
import com.senox.realty.vo.FirefightingStoreInspectionSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/25 11:02
 */
@Service
@RequiredArgsConstructor
public class FirefightingStoreInspectionService
        extends ServiceImpl<FirefightingStoreInspectionMapper, FirefightingStoreInspection> {

    private final FirefightingInspectRealtyService inspectRealtyService;
    private final FirefightingInspectMediaService inspectMediaService;
    private final ApplicationEventPublisher publisher;

    /**
     * 添加商铺消防巡检记录
     * @param taskId
     * @param inspection
     * @param realtySerials
     * @param medias
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addInspection(Long taskId,
                              FirefightingStoreInspection inspection,
                              List<String> realtySerials,
                              List<String> medias) {
        inspection.setStoreAccommodated(BooleanUtils.isTrue(inspection.getStoreAccommodated()));
        if (inspection.getStoreAccommodatedCount() == null) {
            inspection.setStoreAccommodatedCount(0);
        }
        inspection.setFirefightingFacilitiesSatisfied(BooleanUtils.isTrue(inspection.getFirefightingFacilitiesSatisfied()));
        inspection.setFirefightingWallSatisfied(BooleanUtils.isTrue(inspection.getFirefightingWallSatisfied()));
        inspection.setFirefightingStairsSatisfied(BooleanUtils.isTrue(inspection.getFirefightingStairsSatisfied()));
        inspection.setFirefightingLinesSatisfied(BooleanUtils.isTrue(inspection.getFirefightingLinesSatisfied()));
        inspection.setFirefightingAlarmSatisfied(BooleanUtils.isTrue(inspection.getFirefightingAlarmSatisfied()));
        inspection.setFirefightingExitSatisfied(BooleanUtils.isTrue(inspection.getFirefightingExitSatisfied()));
        if (inspection.getInspectDate() == null) {
            inspection.setInspectDate(LocalDate.now());
        }
        prepareRectificationDeadline(inspection);

        InspectResult inspectResult = InspectResult.fromValue(inspection.getInspectResult());
        inspectResult = inspectResult == null ? InspectResult.INIT : inspectResult;
        inspection.setInspectResult(inspectResult.getValue());
        inspection.setCreateTime(LocalDateTime.now());
        inspection.setModifiedTime(LocalDateTime.now());
        save(inspection);
        // 物业
        inspectRealtyService.saveInspectRealities(inspection.getId(), InspectionType.STORE, realtySerials);
        // 多媒体资料
        inspectMediaService.saveMedias(inspection.getId(), InspectionType.STORE, medias);

        // 发送巡检任务事件
        InspectTaskFulfilledEvent event = new InspectTaskFulfilledEvent(this, InspectionType.STORE, inspection.getId());
        event.setTaskId(taskId);
        event.setEnterpriseId(inspection.getEnterpriseId());
        event.setRealtySerials(realtySerials);
        publisher.publishEvent(event);
        return inspection.getId();
    }

    /**
     * 更新商铺消防巡检记录
     * @param inspection
     * @param realtySerials
     * @param medias
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateInspection(FirefightingStoreInspection inspection, List<String> realtySerials, List<String> medias) {
        if (!WrapperClassUtils.biggerThanLong(inspection.getId(), 0L)) {
            return;
        }

        FirefightingStoreInspection dbInspection = findById(inspection.getId());
        if (dbInspection == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        inspection.setInspectDate(inspection.getInspectDate() == null ? dbInspection.getInspectDate() : inspection.getInspectDate());
        prepareRectificationDeadline(inspection);

        inspection.setCreatorId(null);
        inspection.setCreatorName(null);
        inspection.setCreateTime(null);
        inspection.setModifiedTime(LocalDateTime.now());
        updateById(inspection);

        // 物业
        inspectRealtyService.saveInspectRealities(inspection.getId(), InspectionType.STORE, realtySerials);
        // 多媒体资料
        inspectMediaService.saveMedias(inspection.getId(), InspectionType.STORE, medias);
    }

    /**
     * 删除商铺消防巡检记录
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInspection(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return;
        }

        removeById(id);
        inspectRealtyService.deleteByInspectId(id, InspectionType.STORE);
        inspectMediaService.deleteByInspectId(id, InspectionType.STORE);
        // 触发移除事件
        publisher.publishEvent(new InspectTaskDropEvent(this, InspectionType.STORE, id));
    }

    /**
     * 获取商铺消防巡检记录
     * @param id
     * @return
     */
    public FirefightingStoreInspection findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 商铺消防巡检记录统计
     * @param search
     * @return
     */
    public int countInspection(FirefightingStoreInspectionSearchVo search) {
        return getBaseMapper().countStoreInspection(search);
    }

    /**
     * 商铺消防巡检记录查询列表
     * @param search
     * @return
     */
    public List<FirefightingStoreInspection> listInspection(FirefightingStoreInspectionSearchVo search) {
        return getBaseMapper().listStoreInspection(search);
    }

    /**
     * 整改截止日期初始化
     * @param inspection
     */
    private void prepareRectificationDeadline(FirefightingStoreInspection inspection) {
        if (inspection.getRectificationTimeLimit() == null || inspection.getRectificationTimeLimit() < 1) {
            return;
        }

        inspection.setRectificationDeadline(inspection.getInspectDate().minusDays(inspection.getRectificationTimeLimit()));
    }
}

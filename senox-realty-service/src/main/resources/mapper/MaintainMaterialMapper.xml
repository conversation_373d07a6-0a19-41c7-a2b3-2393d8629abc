<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.realty.mapper.MaintainMaterialMapper">

    <resultMap id="Material_Result" type="com.senox.realty.vo.MaintainMaterialDataVo">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="jobId" column="job_id"/>
        <result property="jobNo" column="job_no"/>
        <result property="maintainType" column="maintain_type"/>
        <result property="outNo" column="out_no"/>
        <result property="status" column="status"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="chargeYear" column="charge_year"/>
        <result property="chargeMonth" column="charge_month"/>
        <result property="createTime" column="create_time"/>
        <result property="remark" column="remark"/>
        <result property="handlerDeptId" column="handler_dept_id"/>
        <result property="handlerDeptName" column="handler_dept_name"/>
        <result property="problem" column="problem"/>
        <result property="receivePerson" column="receive_person"/>
        <result property="regionName" column="region_name"/>
        <result property="streetName" column="street_name"/>
        <result property="address" column="address"/>
    </resultMap>


    <select id="countMaintainMaterial" parameterType="com.senox.realty.vo.MaintainMaterialSearchVo" resultType="int">
        select count(m.id) from r_maintain_material m
                LEFT JOIN r_maintain_charge c ON m.order_id = c.order_id and m.job_id = c.job_id
                LEFT JOIN r_maintain_job j ON m.job_id = j.id
        <where>
            <if test="outNo != null and outNo != ''">
                AND m.out_no = #{outNo}
            </if>
            <if test="jobNo != null and jobNo != ''">
                AND j.job_no like CONCAT('%', #{jobNo}, '%')
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="startTime != null">
                AND c.paid_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND c.paid_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="createTimeStart != null">
                AND m.create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND m.create_time <![CDATA[<=]]> #{createTimeEnd}
            </if>
            <if test="state != null and state == 1">
                AND m.out_no is not null
            </if>
            <if test="state != null and state == 0">
                AND m.out_no is null
            </if>
            AND m.is_disabled = 0
        </where>
    </select>

    <select id="listMaintainMaterial" parameterType="com.senox.realty.vo.MaintainMaterialSearchVo" resultMap="Material_Result">
        SELECT
            m.id,
            m.order_id,
            m.job_id,
            m.out_no,
            ( CASE WHEN c.STATUS = 1 THEN 1 ELSE 0 END ) AS STATUS,
            c.total_amount,
            j.job_no,
            j.maintain_type,
            c.charge_year,
            c.charge_month,
            m.create_time,
            m.remark,
            o.handler_dept_id,
            o.handler_dept_name,
            o.problem,
            o.region_name,
            o.street_name,
            o.address,
            IF( LENGTH(ob.receive_person) > 0, ob.receive_person, i.receive_person ) as receive_person
        from r_maintain_material m
            LEFT JOIN r_maintain_charge c ON m.order_id = c.order_id and m.job_id = c.job_id
            LEFT JOIN r_maintain_order o ON o.id = m.order_id
            LEFT JOIN r_maintain_job j ON m.job_id = j.id
            LEFT JOIN (
                SELECT ji.job_id, GROUP_CONCAT( ji.handler_name ) AS receive_person
                FROM r_maintain_job_item ji
                GROUP BY job_id
            ) AS i ON j.id = i.job_id
            LEFT JOIN wms_material_out_bill ob ON ob.id = m.out_no
        <where>
            <if test="outNo != null and outNo != ''">
                AND m.out_no = #{outNo}
            </if>
            <if test="jobNo != null and jobNo != ''">
                AND j.job_no like CONCAT('%', #{jobNo}, '%')
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="startTime != null">
                AND c.paid_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND c.paid_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="createTimeStart != null">
                AND m.create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND m.create_time <![CDATA[<=]]> #{createTimeEnd}
            </if>
            <if test="state != null and state == 1">
                AND m.out_no is not null
            </if>
            <if test="state != null and state == 0">
                AND m.out_no is null
            </if>
            AND m.is_disabled = 0
        </where>
        <if test="orderStr != null and orderStr != ''">
            ORDER BY ${orderStr}
        </if>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <update id="updateOutNo" parameterType="com.senox.realty.vo.MaintainMaterialOutNoVo">
        UPDATE r_maintain_material
        <set>
            <if test="outNo != null and outNo != ''">
                , out_no = #{outNo}
            </if>
            <if test="operatorId != null">
                , modifier_id = #{operatorId}
            </if>
            <if test="operatorName != null">
                , modifier_name = #{operatorName}
            </if>
            , modified_time = NOW()
        </set>
        WHERE id = #{materialId}
    </update>

    <update id="cancelOutBound" parameterType="java.lang.String">
        UPDATE r_maintain_material set out_no = null where out_no = #{outNo}
    </update>

</mapper>

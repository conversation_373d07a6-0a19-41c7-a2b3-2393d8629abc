/* 费项 */
DROP TABLE IF EXISTS r_fee;
CREATE TABLE r_fee(
    id             bigint unsigned   not null   auto_increment,
    name           varchar(50)       not null   default '' comment '费项名称',
    alias          varchar(30)       not null   default '' comment '别名',
    amount         decimal(6, 2)     not null   default 0  comment '金额',
    category       tinyint unsigned  not null   default 0  comment '费项类别',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_fee_name(`name`)
);

/* 经营区域 */
DROP TABLE IF EXISTS r_business_region;
CREATE TABLE r_business_region(
    id             bigint unsigned   not null   auto_increment,
    name           varchar(60)       not null   default '' comment '经营区域',
    cycle_no       varchar(10)       comment '号牌前缀',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_region_name(`name`)
);

/* 街道 */
DROP TABLE IF EXISTS r_street;
CREATE TABLE r_street(
    id             bigint unsigned   not null   auto_increment,
    name           varchar(60)       not null   default '' comment '街道',
    region_id      bigint unsigned   not null   default 0  comment '区域id',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_street_name(`name`, `region_id`)
);
-- ALTER TABLE r_street add index idx_street_region(region_id);

/* 水电价类别 */
DROP TABLE IF EXISTS r_water_electric_price_type;
CREATE TABLE r_water_electric_price_type(
    id             bigint unsigned   not null   auto_increment,
    name           varchar(60)       not null   default '' comment '类型名',
    type           tinyint unsigned  not null   default 0  comment '单价类别，1水；2电',
    price          decimal(8, 4)     not null   default 0  comment '价格',
    remark         varchar(200)      comment '备注',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);


/* 物业管理 */
DROP TABLE IF EXISTS r_realty;
CREATE TABLE r_realty(
    id             bigint unsigned    not null   auto_increment,
    serial_no      varchar(30)        not null   default '' comment '编号',
    name           varchar(60)        not null   default '' comment '物业名称',
    nature         tinyint unsigned   not null   default 0  comment '性质，1公司;2销售;3安置;4长租',
    area           decimal(6, 2)      not null   default 0  comment '面积',
    region_id      bigint unsigned    not null   default 0  comment '区域id',
    region_name    varchar(60)        comment '区域名',
    street_id      bigint unsigned    not null   default 0  comment '街道id',
    street_name    varchar(60)        comment '街道名',
    address        varchar(500)       comment '地址',
    profession_id    bigint unsigned  not null   default 0 comment '行业id',
    profession_name  varchar(60)      comment '行业',
    owner_id         bigint unsigned  not null   default 0 comment '业主id',
    owner_name       varchar(60)      comment '业主名',
    order_num      smallint unsigned             default 0  comment '排序号',
    is_znw_synced  tinyint(1)         not null   default 0  comment '中农网同步',
    is_disabled    tinyint(1)         not null   default 0  comment '禁用',
    creator_id     bigint unsigned    not null   default 0  comment '创建人id',
    creator_name   varchar(30)        not null   default '' comment '创建人姓名',
    create_time    datetime           not null   comment '创建时间',
    modifier_id    bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name  varchar(30)        not null   default '' comment '修改人姓名',
    modified_time  datetime           not null   comment '修改时间',
    primary key (id),
    unique key uk_realty_serialNo(serial_no)
);

DROP TABLE IF EXISTS r_realty_ext;
CREATE TABLE r_realty_ext(
    realty_id      bigint unsigned    not null   default 0 comment '物业id',
    water_readings    int unsigned    not null   default 0 comment '水表读数',
    electric_readings int unsigned    not null   default 0 comment '电表读数',
    water_price       decimal(10, 4)  not null   default 0 comment '水单价',
    electric_price    decimal(10, 4)  not null   default 0 comment '电单价',
    water_consume_unit int unsigned   not null   default 0 comment '水消费单元',
    electric_consume_unit int unsigned not null  default 0 comment '电消费单元',
    rent_tax_code varchar(25) default null comment '租金税收编码',
    remark         varchar(500)       comment '备注',
    is_disabled    tinyint(1)         not null   default 0  comment '禁用',
    creator_id     bigint unsigned    not null   default 0  comment '创建人id',
    creator_name   varchar(30)        not null   default '' comment '创建人姓名',
    create_time    datetime           not null   comment '创建时间',
    modifier_id    bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name  varchar(30)        not null   default '' comment '修改人姓名',
    modified_time  datetime           not null   comment '修改时间',
    primary key (realty_id)
);
/* 合同 */
DROP TABLE IF EXISTS r_contract;
CREATE TABLE r_contract(
    id             bigint unsigned    not null   auto_increment,
    contract_no    varchar(30)        not null   default '' comment '合同编号',
    order_no       varchar(30)        not null   default '' comment '手工合同编号',
    type           tinyint unsigned   not null   default 0  comment '合同类型, 1租赁合同;2物业合同;3返租合同;4代租合同;5代收租合同',
    realty_id      bigint unsigned    not null   default 0  comment '物业id',
    realty_name    varchar(60)        not null   default '' comment '物业名',
    customer_id    bigint unsigned    not null   default 0  comment '客户id',
    customer_name  varchar(60)        not null   default '' comment '客户名',
    sign_date      date               not null   comment '签约时间',
    start_date     date               not null   comment '开始时间',
    end_date       date               not null   comment '结束时间',
    status         tinyint(4) unsigned not null  default 0  comment '状态, 0停用;1未生效;2启用',
    stop_by        bigint unsigned    comment '停用人',
    stop_time      datetime           comment '停用时间',
    ic_card_no     varchar(20)        comment 'IC卡号',
    is_temporary_rent tinyint(1)      not null   default 0  comment '临租',
    is_renamed     tinyint(1)         not null   default 0  comment '转名',
    is_znw_synced  tinyint(1)         not null   default 0  comment '中农网同步',
    is_disabled    tinyint(1)         not null   default 0  comment '禁用',
    creator_id     bigint unsigned    not null   default 0  comment '创建人id',
    creator_name   varchar(30)        not null   default '' comment '创建人姓名',
    create_time    datetime           not null   comment '创建时间',
    modifier_id    bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name  varchar(30)        not null   default '' comment '修改人姓名',
    modified_time  datetime           not null   comment '修改时间',
    primary key (id),
    unique key uk_contract_no(contract_no)
);
ALTER TABLE r_contract
    ADD category tinyint unsigned default 0 not null comment '类别(1:企业;2:个人)' AFTER `type`;

-- ALTER TABLE r_contract ADD INDEX idx_contract_realty(realty_id);
-- ALTER TABLE r_contract ADD INDEX idx_contract_customer(customer_id);

/* 合同扩展信息 */
DROP TABLE IF EXISTS r_contract_ext;
CREATE TABLE r_contract_ext(
    contract_id    bigint unsigned    not null   comment '合同id',
    cost_type      tinyint unsigned   not null   default 0 comment '交款方式，1现金；2银行托收；3支票；4转账',
    bank_name          varchar(50)    comment '银行名',
    bank_account_no    varchar(50)    comment '银行账号',
    bank_account_name    varchar(50)  comment '银行账户名',
    bank_account_idcard  varchar(50)  comment '银行账户身份证',
    first_rate       decimal(8, 2)    comment '首月手续费率',
    monthly_rate     decimal(8, 2)    comment '每月手续费率',
    monthly_fee_abs  decimal(8, 2)    comment '每月手续费绝对值',
    water_price_type    bigint unsigned  comment '水价类别',
    electric_price_type bigint unsigned  comment '电价类别',
    penalty_start_date  tinyint unsigned comment '滞纳金开始日期',
    penalty_rate        decimal(8, 2)    comment '滞纳金比例',
    archive_url    varchar(200)       comment '归档url',
    remark         varchar(200)       comment '备注',
    is_disabled    tinyint(1)         not null   default 0  comment '禁用',
    creator_id     bigint unsigned    not null   default 0  comment '创建人id',
    creator_name   varchar(30)        not null   default '' comment '创建人姓名',
    create_time    datetime           not null   comment '创建时间',
    modifier_id    bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name  varchar(30)        not null   default '' comment '修改人姓名',
    modified_time  datetime           not null   comment '修改时间',
    primary key (contract_id)
);

/* 合同费项 */
DROP TABLE IF EXISTS r_contract_fee;
CREATE TABLE r_contract_fee(
    id             bigint unsigned   not null   auto_increment,
    contract_id    bigint unsigned   not null   default 0 comment '合同id',
    fee_id         bigint unsigned   not null   default 0 comment '费项id',
    category       tinyint unsigned  not null   default 0 comment '类别，0默认收费，1阶段收费',
    `period`       tinyint unsigned  not null   default 0 comment '期间',
    amount         decimal(8, 4)     not null   default 0 comment '金额',
    rent_free_period tinyint unsigned  not null default 0 comment '免租期',
    start_date     date              not null   comment '开始日期',
    end_date       date              not null   comment '结束日期',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE r_contract_fee ADD INDEX idx_contract_fee_interval(contract_id, fee_id, start_date, end_date);

/* 物业账单 */
DROP TABLE IF EXISTS r_realty_bill;
CREATE TABLE r_realty_bill(
    id              bigint unsigned   not null   auto_increment,
    bill_year_month varchar(10)       not null   comment '账单年月',
    bill_year       smallint unsigned not null   comment '账单年份',
    bill_month      tinyint unsigned  not null   comment '账单月份',
    realty_id       bigint unsigned   not null   comment '物业id',
    contract_no     varchar(30)       not null   comment '合同编号',
    amount          decimal(10, 2)    not null   default 0  comment '金额',
    penalty_amount  decimal(10, 2)    not null   default 0  comment '滞纳金',
    penalty_date    date              not null   comment '滞纳金开始日期',
    penalty_ignore  tinyint(1)        not null   default 0  comment '减免滞纳金',
    penalty_ignore_amount  decimal(10, 2) not null default 0  comment '滞纳金减免金额',
    total_amount    decimal(10, 2)    not null   default 0  comment '总金额',
    paid_amount     decimal(10, 2)    not null   default 0  comment '支付金额',
    paid_still_amount decimal(10, 2)  default 0  comment '待收费金额',
    refund_amount   decimal(10, 2)    default 0  comment '退费金额',
    penalty_paid_amount decimal(10, 2) not null  default 0  comment '实收滞纳金',
    paid_time       datetime          comment '支付时间',
    refund_time     datetime          comment '退费时间',
    remote_order_id bigint unsigned   not null   default 0  comment '订单id',
    refund_order_id bigint unsigned   not null   default 0  comment '退费订单id',
    status         tinyint unsigned   not null   default 0  comment '账单状态：0初始化；1已支付',
    send           tinyint(1)         not null   default 0  comment '下发状态：0初始化；1已下发',
    send_time      datetime           comment '下发时间',
    remark         varchar(255)      comment '备注',
    toll_man_id    bigint unsigned   not null   default 0  comment '收费员',
    refund_man_id  bigint unsigned   not null   default 0  comment '退费人',
    bill_serial    varchar(10)       comment '票据号',
    receipt        tinyint(1) unsigned  not null   default 0  comment '开发票',
    receipt_man    bigint unsigned   not null   default 0  comment '开票人',
    receipt_remark varchar(100)      comment '开票备注',
    receipt_time   datetime          comment '开票时间',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE r_realty_bill ADD INDEX idx_realty_bill_realty(realty_id);
-- ALTER TABLE r_realty_bill ADD INDEX idx_realty_bill_year_month(bill_year_month);
-- ALTER TABLE r_realty_bill ADD INDEX idx_realty_bill_ym(bill_year, bill_month);
-- ALTER TABLE r_realty_bill ADD INDEX idx_realty_bill_contractNo(contract_no);
-- ALTER TABLE r_realty_bill ADD INDEX idx_realty_bill_remoteOrder(remote_order_id);


/* 物业账单明细 */
DROP TABLE IF EXISTS r_realty_bill_item;
CREATE TABLE r_realty_bill_item(
    id             bigint unsigned   not null   auto_increment,
    bill_id        bigint unsigned   not null   default 0  comment '账单id',
    fee_id         bigint unsigned   not null   default 0  comment '费项id',
    fee_name       varchar(50)       not null   default '' comment '费项名',
    amount         decimal(10, 2)    not null   default 0  comment '金额',
    attr1          varchar(50)       comment '保留字段1',
    attr2          varchar(50)       comment '保留字段2',
    status         tinyint unsigned  not null   default 0  comment '账单状态：0初始化；1已支付',
    paid_time      datetime          comment '支付时间',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_realty_bill_item_fee(bill_id, fee_id)
);
-- create index idx_id_amount_receipt_status on r_realty_bill_item (bill_id, amount, receipt_status);

DROP TABLE IF EXISTS r_realty_payoff;
CREATE TABLE r_realty_payoff(
    id              bigint unsigned   not null   auto_increment,
    bill_year_month varchar(10)       not null   comment '账单年月',
    bill_year       smallint unsigned not null   comment '账单年份',
    bill_month      tinyint unsigned  not null   comment '账单月份',
    realty_id       bigint unsigned   not null   comment '物业id',
    contract_no     varchar(30)       not null   comment '合同编号',
    amount          decimal(10, 2)    not null   default 0  comment '金额',
    status          tinyint unsigned  not null   default 0  comment '账单状态：0初始化；1已支付',
    paid_time       datetime          comment '支付时间',
    remark          varchar(255)      comment '备注',
    is_disabled     tinyint(1)        not null   default 0  comment '禁用',
    creator_id      bigint unsigned   not null   default 0  comment '创建人id',
    creator_name    varchar(30)       not null   default '' comment '创建人姓名',
    create_time     datetime          not null   comment '创建时间',
    modifier_id     bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name   varchar(30)       not null   default '' comment '修改人姓名',
    modified_time   datetime          not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE r_realty_payoff ADD INDEX idx_realty_payoff_ym(bill_year, bill_month);
-- ALTER TABLE r_realty_payoff ADD INDEX idx_realty_payoff_realty(realty_id);
-- ALTER TABLE r_realty_payoff ADD INDEX idx_realty_payoff_contractNo(contract_no);

DROP TABLE IF EXISTS r_realty_payoff_item;
CREATE TABLE r_realty_payoff_item(
    id             bigint unsigned   not null   auto_increment,
    bill_id        bigint unsigned   not null   default 0  comment '账单id',
    fee_id         bigint unsigned   not null   default 0  comment '费项id',
    fee_name       varchar(50)       not null   default '' comment '费项名',
    amount         decimal(10, 2)    not null   default 0  comment '金额',
    status         tinyint unsigned  not null   default 0  comment '账单状态：0初始化；1已支付',
    paid_time      datetime          comment '支付时间',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_realty_payoff_item_fee(bill_id, fee_id)
);


DROP TABLE IF EXISTS r_realty_bill_withhold;
CREATE TABLE r_realty_bill_withhold(
    id             bigint unsigned   not null   auto_increment,
    bill_id        bigint unsigned   not null   default 0  comment '账单id',
    is_offer       tinyint(1)        not null   default 0  comment '报盘',
    offer_amount   decimal(10, 2)    not null   default 0  comment '报盘金额',
    offer_bank     varchar(50)       comment '银行',
    offer_account_no      varchar(50)    comment '银行账号',
    offer_account_name    varchar(50)  comment '银行账户名',
    offer_account_idcard  varchar(50)  comment '银行账户身份证',
    offer_time     datetime          comment '报盘时间',
    is_back        tinyint(1)        not null   default 0  comment '回盘',
    back_amount    decimal(10, 2)    not null   default 0 comment '回盘金额',
    back_account_no   varchar(50)    comment '回盘账号',
    back_account_name varchar(50)    comment '回盘户名',
    back_time      datetime          comment '回盘时间',
    primary key(id),
    unique key (bill_id)
);

/* 物业账单银行托收 */
DROP TABLE IF EXISTS r_bank_withhold;
CREATE TABLE r_bank_withhold(
    bill_year      smallint unsigned not null   comment '账单年份',
    bill_month     tinyint unsigned  not null   comment '账单月份',
    is_offer       tinyint(1)        not null   default 0 comment '托收报盘：0 未报盘；1 已报盘；',
    offer_time     datetime          comment '报盘时间',
    offer_user     bigint unsigned   comment '报盘人',
    is_back        tinyint(1)        not null   default 0 comment '托收回盘：0 未回盘；1 已回盘',
    back_time      datetime          comment '回盘时间',
    back_user      bigint unsigned   comment '回盘人',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (bill_year, bill_month)
);


DROP TABLE IF EXISTS r_realty_bill_penalty_setting;
CREATE TABLE r_realty_bill_penalty_setting(
    bill_year_month varchar(10)      not null   comment '账单年月',
    penalty_date   date              comment '滞纳金开始日期',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key(bill_year_month)
);

DROP TABLE IF EXISTS r_realty_deposit;
CREATE TABLE r_realty_deposit(
    id             bigint unsigned   not null   auto_increment,
    contract_id    bigint unsigned   not null   default 0  comment '合同id',
    customer_id    bigint unsigned   not null   default 0  comment '客户id',
    customer_name  varchar(60)       comment '客户名',
    realty_id      bigint unsigned   not null   default 0  comment '物业id',
    fee_id         bigint unsigned   not null   comment '费项id',
    fee_name       varchar(50)       comment '费项名',
    amount         decimal(8, 2)     not null   default 0  comment '金额',
    remark         varchar(500)      comment '备注',
    status         tinyint unsigned  not null   default 0  comment '状态，0初始化，1已支付，10已退费',
    operate_date   date              not null   comment '开单日期',
    toll_serial    varchar(15)        not null   default '' comment '收费单号',
    toll_by        bigint unsigned   not null   default 0  comment '收费员',
    toll_time      datetime          comment '收费时间',
    remote_order_id bigint unsigned  not null   default 0  comment '订单id',
    refund_amount  decimal(10, 2)    not null   default 0  comment '退费金额',
    refund_serial  varchar(15)       not null   default '' comment '退费单号',
    refund_by      bigint unsigned   not null   default 0  comment '退费人',
    refund_time    datetime          comment '退费时间',
    refund_order_id bigint unsigned  not null   default 0  comment '退费订单id',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);
-- alter table r_realty_deposit add index idx_realty_deposit_contract(`contract_id`);
-- alter table r_realty_deposit add index idx_realty_deposit_customer(`customer_id`);
-- alter table r_realty_deposit add index idx_realty_deposit_realty(`realty_id`);
-- alter table r_realty_deposit add index idx_realty_deposit_operateDate(`operate_date`);

DROP TABLE IF EXISTS r_one_time_fee;
CREATE TABLE r_one_time_fee(
    id             bigint unsigned   not null   auto_increment,
    name           varchar(60)       not null   default '' comment '一次性费项名',
    is_refrigeration tinyint(1)      not null   default 0  comment '冷藏费',
    mobile_editable tinyint(1)       not null   default 0  comment '移动端显示(0:不显示,1:显示)',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_one_time_fee_name(name)
);

DROP TABLE IF EXISTS r_one_time_fee_department;
CREATE TABLE r_one_time_fee_department(
    fee_id         bigint unsigned   not null   default 0  comment '一次性费项id',
    department_id  bigint unsigned   not null   default 0  comment '部门id',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (fee_id, department_id)
);

DROP TABLE IF EXISTS r_one_time_fee_bill;
CREATE TABLE r_one_time_fee_bill(
    id             bigint unsigned   not null   auto_increment,
    bill_no        varchar(20)       not null   comment '单号',
    bill_year      smallint unsigned not null   comment '年份',
    bill_month     tinyint unsigned  not null   comment '月份',
    fee_id         bigint unsigned   not null   default 0  comment '一次性收入费项',
    fee_name       varchar(60)       comment '费项名',
    amount         decimal(10, 2)    not null   default 0  comment '金额',
    department_id  bigint unsigned   not null   default 0  comment '部门id',
    department_name varchar(60)      comment '部门名',
    customer_serial varchar(20)      not null   default '' comment '客户编号',
    customer       varchar(50)       comment '客户',
    realty_id      bigint unsigned   not null   default 0  comment '物业id',
    remark         varchar(500)      comment '备注',
    status         tinyint unsigned  not null   default 0  comment '状态，0初始化，1已支付，10已退费',
    operate_by     bigint unsigned   not null   default 0  comment '经手人',
    operate_date   date              not null   comment '录入日期',
    toll_serial    varchar(8)        not null   default '' comment '收费单号',
    toll_by        bigint unsigned   not null   default 0  comment '收费员',
    toll_time      datetime          comment '收费时间',
    refund_amount  decimal(10, 2)    not null   default 0  comment '退费金额',
    refund_serial  varchar(8)        not null   default '' comment '退费单号',
    refund_by      bigint unsigned   not null   default 0  comment '退费人',
    refund_time    datetime          comment '退费时间',
    openid                varchar(64)                   null comment '微信用户id',
    remote_order_id       bigint unsigned  default '0'  not null comment '订单id',
    refund_order_id       bigint unsigned  default '0'  not null comment '退费订单id',
    contact_phone         varchar(20)      default ''   not null comment '联系方式',
    wechat_creator_openid varchar(32)                   null comment '微信端创建人openid',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id),
    unique key uk_one_time_fee_bill_no(bill_no)
);
-- ALTER TABLE r_one_time_fee_bill ADD INDEX idx_one_time_fee_bill_realty(realty_id);
-- ALTER TABLE r_one_time_fee_bill ADD INDEX idx_one_time_fee_bill_year_month(bill_year, bill_month);
-- ALTER TABLE r_one_time_fee_bill ADD INDEX idx_one_time_fee_operate_date(operate_date);
-- ALTER TABLE r_one_time_fee_bill ADD INDEX idx_one_time_fee_bill_customer_serial(customer_serial);


DROP TABLE IF EXISTS u_customer;
CREATE TABLE u_customer(
    id             bigint unsigned   not null    auto_increment,
    serial_no      varchar(20)       not null    comment '编号',
    name           varchar(60)       not null    comment '客户名称',
    idcard         varchar(30)       not null    default '' comment '证件号码',
    idcard_type    tinyint unsigned  not null    default 0,
    gender         tinyint unsigned  comment '性别，1男；2女；3其他',
    nation         varchar(20)       comment '民族',
    born_date      date              comment '出生日期',
    native_place   varchar(50)       comment '籍贯',
    address        varchar(500)      comment '地址',
    telephone      varchar(20)       not null    default '' comment '手机',
    email          varchar(30)       comment '邮箱',
    province_id    bigint unsigned   not null default 0 comment '省/直辖市id',
    province_name  varchar(30)       comment '省/直辖市名',
    city_id        bigint unsigned   not null default 0 comment '市/区id',
    city_name      varchar(30)       comment '市/区名',
    workplace_region_id    bigint unsigned comment '工作地 - 经营区域id',
    workplace_region_name  varchar(200)    comment '工作地 - 经营区域名',
    workplace_street_id    bigint unsigned comment '工作地 - 街道id',
    workplace_street_name  varchar(200)    comment '工作地 - 街道名',
    workplace_address      varchar(200)    comment '工作地 - 地址',
    profession_id   bigint unsigned   not null    default 0  comment '行业id',
    profession_name varchar(30)       comment '行业名',
    job_title       varchar(20)       comment '工作职位',
    is_nat_tested           tinyint(1)   not null  default 0  comment '是否做核酸检测',
    is_covid19_vaccination  tinyint(1)   not null  default 0  comment '是否注射了新冠疫苗',
    is_znw_synced   tinyint(1)        not null    default 0  comment '中农网同步',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key (id)
);

DROP TABLE IF EXISTS r_realty_bill_we;
CREATE TABLE r_realty_bill_we (
    id                      bigint unsigned   not null    auto_increment,
    bill_year               smallint unsigned not null    comment '年份',
    bill_month              tinyint unsigned  not null    comment '月份',
    bill_id                 bigint unsigned   not null    comment '账单id',
    realty_serial           varchar(30)       not null    default '' comment '物业编号',
    realty_alias_serial     varchar(30)       not null    default '' comment '副档编号',
    customer_id             bigint unsigned   not null    default 0  comment '客户id',
    last_water_readings     int unsigned      not null    default 0  comment '上次水读数',
    water_readings          int unsigned      not null    default 0  comment '本次水读数',
    water_share             int unsigned      not null    default 0  comment '水公摊',
    water_cost              int unsigned      not null    default 0  comment '用水量',
    water_price             decimal(10, 4)    not null    default 0  comment '水价格',
    water_amount            decimal(10, 2)    not null    default 0  comment '水费',
    last_electric_readings  int unsigned      not null    default 0  comment '上次电读数',
    electric_readings       int unsigned      not null    default 0  comment '本次电读数',
    electric_share          int unsigned      not null    default 0  comment '电公摊',
    electric_cost           int unsigned      not null    default 0  comment '用电量',
    electric_price          decimal(10, 4)    not null    default 0  comment '电价格',
    electric_amount         decimal(10, 2)    not null    default 0  comment '电费',
    total_amount            decimal(10, 2)    not null    default 0  comment '水电合计金额',
    last_record_date        date              comment '上次抄表日期',
    record_date             date              comment '抄表日期',
    is_disabled             tinyint(1)        not null    default 0  comment '禁用',
    creator_id              bigint unsigned   not null    default 0  comment '创建人id',
    creator_name            varchar(30)       not null    default '' comment '创建人姓名',
    create_time             datetime          not null    comment '创建时间',
    modifier_id             bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name           varchar(30)       not null    default '' comment '修改人姓名',
    modified_time           datetime          not null    comment '修改时间',
    primary key(id)
);
-- ALTER TABLE r_realty_bill_we ADD INDEX idx_realty_bill_we_yearMonth(bill_year, bill_month);
-- ALTER TABLE r_realty_bill_we ADD INDEX idx_realty_bill_we_billId(bill_id);
-- ALTER TABLE r_realty_bill_we ADD INDEX idx_realty_bill_we_realtySerial(realty_serial);
-- ALTER TABLE r_realty_bill_we ADD INDEX idx_realty_bill_we_realtyAliasSerial(realty_alias_serial);

DROP TABLE IF EXISTS r_realty_alias;
CREATE TABLE r_realty_alias (
    id              bigint unsigned   not null    auto_increment,
    realty_id       bigint unsigned   not null    default 0  comment '物业id',
    serial_no       varchar(30)       not null    default '' comment '别名档号',
    name            varchar(60)       not null    default '' comment '档别名称',
    water_readings    int unsigned    not null    default 0 comment '水表读数',
    electric_readings int unsigned    not null    default 0 comment '电表读数',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key(`id`),
    unique key uk_realty_alias_serial(`serial_no`)
);
-- ALTER TABLE r_realty_alias ADD INDEX idx_realty_alias_realtyId(realty_id);

DROP TABLE IF EXISTS r_realty_we;
CREATE TABLE r_realty_we (
    id              bigint unsigned   not null    auto_increment,
    bill_year              smallint unsigned not null    comment '年份',
    bill_month             tinyint unsigned  not null    comment '月份',
    `type`                 tinyint unsigned default 0 not null comment '读数类型(1:月缴费账单读数)',
    realty_serial          varchar(30)       not null    comment '物业编号',
    last_water_readings    int unsigned      not null    comment '上次水读数',
    water_readings         int unsigned      not null    comment '本次水读数',
    water_consume_unit     int unsigned      not null  default 0 comment '水消费单位',
    water_base             int unsigned      not null  default 0 comment '水低消',
    last_electric_readings int unsigned      not null    comment '上次电读数',
    electric_readings      int unsigned      not null    comment '本次电读数',
    electric_consume_unit int unsigned       not null  default 0 comment '电消费单位',
    electric_base          int unsigned      not null  default 0 comment '电低消',
    last_record_date       date              comment '上次抄表日期',
    record_date            date              comment '抄表日期',
    is_man_made tinyint(1) not null default 0 comment '是否人工产生',
    we_bill_id             bigint unsigned   not null    default 0 comment '水电账单id',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key(`id`)
);
-- ALTER TABLE r_realty_we ADD INDEX idx_realty_we_year_month(bill_year, bill_month);
-- ALTER TABLE r_realty_we ADD INDEX idx_realty_we_realty_serial(realty_serial);


DROP TABLE IF EXISTS r_maintain_order;
CREATE TABLE r_maintain_order(
    id              bigint unsigned   not null    auto_increment,
    order_no        varchar(30)       not null    default '' comment '维修订单号',
    maintain_type   tinyint unsigned  not null    default 0  comment '类型 1 公共; 2 土建; 3 水电; 10 其他',
    customer_name   varchar(60)       not null    default '' comment '客户名',
    contact         varchar(20)       not null    default '' comment '联系方式',
    region_name       varchar(40)        not null    default ''  comment '区域名',
    street_name       varchar(40)        not null    default '' comment '街道名',
    address         varchar(60)       not null    default '' comment '地址',
    problem         varchar(255)                  comment '问题描述',
    job_no          varchar(30)       not null    default '' comment '派工单号',
    handler_name    varchar(60)                   comment '处理人',
    handler_dept_id bigint unsigned      default 0  comment '处理人部门id',
    handler_dept_name varchar(30)                 comment '处理人部门',
    management_dept_id bigint unsigned      default 0  comment '管理所属部门id',
    management_dept_name varchar(30)       comment '管理所属部门',
    finish_time     datetime                      comment '完成时间',
    status          smallint          not null    default 0  comment '状态 0 待受理; 1 正在处理； 10 已处理；50 无法处理；99 取消订单；',
    user_confirm    tinyint(1)        not null    default 0  comment '用户验收',
    evaluate_rating  smallint          not null    default 0  comment '评价星级1-5',
    evaluate_time  datetime           comment '评价时间',
    evaluate_openid  varchar(64)       not null    default '' comment '评价人openid',
    evaluate        varchar(255)                  comment '评价信息',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    create_openid   varchar(64)       not null    default '' comment '微信创建用户',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key(`id`),
    unique key uk_maintain_orderNo(`order_no`)
);
-- ALTER TABLE r_maintain_order ADD INDEX idx_maintain_createOpenid(`create_openid`);
-- ALTER TABLE r_maintain_order ADD COLUMN management_dept_id bigint unsigned      default 0  comment '管理所属部门id' AFTER handler_dept_name;
-- ALTER TABLE r_maintain_order ADD COLUMN management_dept_name varchar(30)       comment '管理所属部门' AFTER management_dept_id;

-- ALTER TABLE r_maintain_order ADD COLUMN evaluate_rating  smallint          not null    default 0  comment '评价星级1-5' AFTER user_confirm;
-- ALTER TABLE r_maintain_order ADD COLUMN evaluate_time  datetime           comment '评价时间' AFTER evaluate_rating;
-- ALTER TABLE r_maintain_order ADD COLUMN evaluate_openid  varchar(64)       not null    default '' comment '评价人openid' AFTER evaluate_time;
-- ALTER TABLE r_maintain_order ADD COLUMN evaluate        varchar(255)                  comment '评价信息' AFTER evaluate_openid;

-- ALTER TABLE r_maintain_order ADD COLUMN region_name       varchar(40)        not null    default ''  comment '区域名' AFTER contact;
-- ALTER TABLE r_maintain_order ADD COLUMN street_name       varchar(40)        not null    default '' comment '街道名' AFTER region_name;

DROP TABLE IF EXISTS r_maintain_media;
CREATE TABLE r_maintain_media(
    id              bigint unsigned   not null    auto_increment,
    order_id        bigint unsigned   not null    default 0 comment '订单id',
    job_id          bigint unsigned   not null    default 0 comment '任务id',
    job_item_id     bigint unsigned   not null    default 0 comment '任务子项id',
    media_url       varchar(100)      comment '多媒体资料访问连接',
    modified_time   datetime          comment '修改时间',
    primary key(id)
);
-- ALTER TABLE r_maintain_media ADD INDEX idx_maintain_media_orderId_jobId_jobItemId(`order_id`, `job_id`, `job_item_id`);


DROP TABLE IF EXISTS r_maintain_job;
CREATE TABLE r_maintain_job(
    id              bigint unsigned   not null    auto_increment,
    order_id        bigint unsigned   not null    default 0  comment '订单id',
    job_no          varchar(30)       not null    default '' comment '派工单号',
    maintain_type   tinyint unsigned  not null    default 0  comment '类型 1 公共; 2 土建; 3 水电; 10 其他',
    status          tinyint unsigned  not null    default 0,
    dispatch_type tinyint(1) unsigned  not null  default 0 comment '派工类别 0：处理，1：审核',
    multiple_complete  tinyint(1) unsigned  not null  default 0 comment '是否多个完成 0：否，1：是',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key(`id`),
    unique key uk_maintainence_job_no(`job_no`)
);
-- ALTER TABLE r_maintain_job ADD INDEX idx_maintain_job_orderId(`order_id`);
-- ALTER TABLE r_maintain_job ADD COLUMN dispatch_type tinyint(1) unsigned  not null  default 0 comment '派工类别 0：处理，1：审核' AFTER remark;
-- ALTER TABLE r_maintain_job ADD COLUMN multiple_complete  tinyint(1) unsigned  not null  default 0 comment '是否多个完成 0：否，1：是' AFTER dispatch_type;

DROP TABLE IF EXISTS r_maintain_job_item;
CREATE TABLE r_maintain_job_item(
    id              bigint unsigned   not null    auto_increment,
    job_id          bigint unsigned   not null    default 0  comment '派工id',
    handler_id      bigint unsigned   not null    default 0 comment '处理人id',
    handler_name    varchar(30)                   comment '处理人',
    remark          varchar(255)                  comment '备注',
    handler_status          tinyint unsigned  not null    default 0,
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key(`id`)
);

DROP TABLE IF EXISTS r_maintain_material;
CREATE TABLE r_maintain_material(
    id              bigint unsigned   not null    auto_increment,
    order_id        bigint unsigned   not null    default 0  comment '订单id',
    job_id          bigint unsigned   not null    default 0  comment '任务id',
    out_no          varchar(30)       comment '出库单号',
    remark          varchar(255)                  comment '备注',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key(`id`)
);
-- ALTER TABLE r_maintain_material ADD INDEX idx_maintain_material_orderId_jobId(`order_id`, `job_id`);


DROP TABLE IF EXISTS r_maintain_material_item;
CREATE TABLE r_maintain_material_item(
    id              bigint unsigned   not null    auto_increment,
    material_id       bigint unsigned   not null    default 0  comment '维修物料id',
    material_code   bigint unsigned   not null    default 0  comment '物料code',
    material_name   varchar(50)       not null    default '' comment '物料名称',
    price           decimal(8, 2)     not null    default 0  comment '物料成本单价',
    quantity        smallint unsigned not null    default 0  comment '物料数量',
    amount          decimal(12, 2)    not null    default 0  comment '物理金额',
    remark          varchar(255)                  comment '备注',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key(`id`)
);


DROP TABLE IF EXISTS r_maintain_charge;
CREATE TABLE r_maintain_charge(
    id              bigint unsigned   not null    auto_increment,
    charge_no       varchar(30)       not null    default '' comment '收费单号',
    order_id        bigint unsigned   not null    default 0  comment '订单id',
    job_id          bigint unsigned   not null    default 0  comment '任务id',
    charge_year      smallint unsigned not null   default 0  comment '收费年份',
    charge_month     tinyint unsigned  not null   default 0  comment '收费月份',
    total_amount    decimal(10, 2)   not null   default 0  comment '合计',
    status           tinyint unsigned  not null    default 0 comment '收费状态：0初始化；1已支付',
    paid_time       datetime                    comment '支付时间',
    remote_order_id bigint unsigned  not null   default 0  comment '订单id',
    toll_serial    varchar(8)        not null   default '' comment '收费单号',
    toll_man_id     bigint unsigned   not null  default 0  comment '收费员',
    remark          varchar(255)                  comment '备注',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key(`id`)
);
-- ALTER TABLE r_maintain_charge ADD INDEX idx_maintain_charge_orderId_jobId(`order_id`, `job_id`);

DROP TABLE IF EXISTS r_maintain_charge_item;
CREATE TABLE r_maintain_charge_item(
    id              bigint unsigned   not null    auto_increment,
    charge_id       bigint unsigned   not null    default 0  comment '物维收费id',
    fee_id          bigint unsigned   not null    default 0  comment '费用类别',
    fee_title       varchar(50)                   comment '费用名',
    fee_item_code   bigint unsigned   not null    default 0  comment '费项编码',
    fee_item_name   varchar(50)                   comment '费项名称',
    price           decimal(8, 2)     not null    default 0  comment '物料单价',
    quantity        smallint unsigned not null    default 0  comment '物料数量',
    amount          decimal(12, 2)    not null    default 0  comment '物理金额',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key(`id`)
);

/* 物维单日报表 */
DROP TABLE IF EXISTS r_maintain_order_day_report;
CREATE TABLE r_maintain_order_day_report(
    id             bigint unsigned   not null   auto_increment,
    report_date      date              not null   comment '报表日期',
    management_dept_id bigint unsigned      default 0  comment '管理所属部门id',
    management_dept_name varchar(30)       comment '管理所属部门',
    add_singular_numbers      int  default 0  not null comment '新增单数',
    complete_singular_numbers      int  default 0  not null comment '完成单数',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE r_maintain_order_day_report ADD INDEX idx_report_date(`report_date`);

/* 物维单月报表 */
DROP TABLE IF EXISTS r_maintain_order_month_report;
CREATE TABLE r_maintain_order_month_report(
    id             bigint unsigned   not null   auto_increment,
    report_year_month varchar(20) NOT NULL COMMENT '月报年月',
    management_dept_id bigint unsigned      default 0  comment '管理所属部门id',
    management_dept_name varchar(30)       comment '管理所属部门',
    add_singular_numbers      int  default 0  not null comment '新增单数',
    complete_singular_numbers      int  default 0  not null comment '完成单数',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE r_maintain_order_month_report ADD INDEX idx_report_year_month(`report_year_month`);

DROP TABLE IF EXISTS r_maintain_evaluate_media;
CREATE TABLE r_maintain_evaluate_media(
    id              bigint unsigned   not null    auto_increment,
    order_id        bigint unsigned   not null    default 0 comment '订单id',
    media_url       varchar(100)      comment '多媒体资料访问连接',
    modified_time   datetime          comment '修改时间',
    primary key(id)
);
-- ALTER TABLE r_maintain_evaluate_media ADD INDEX idx_maintain_evaluate_media_order_id(`order_id`);

DROP TABLE IF EXISTS u_department;
CREATE TABLE u_department
(
    id            bigint unsigned  not null      auto_increment,
    name          varchar(64)                    not null comment '部门名',
    full_name     varchar(200)                   null comment '部门全称',
    parent_id     bigint unsigned   default '0'  not null comment '父部门id',
    order_no      smallint unsigned default '50' not null comment '排序',
    is_disabled   tinyint(1)        default 0    not null comment '禁用',
    creator_id    bigint unsigned   default '0'  not null comment '创建人id',
    creator_name  varchar(30)       default ''   not null comment '创建人姓名',
    create_time   datetime                       not null comment '创建时间',
    modifier_id   bigint unsigned   default '0'  not null comment '修改人id',
    modifier_name varchar(30)       default ''   not null comment '修改人姓名',
    modified_time datetime                       not null comment '修改时间',
    primary key (`id`),
    constraint uk_department_name
        unique (parent_id, name)
);
-- ALTER TABLE r_realty_we ADD INDEX idx_realty_we_year_month(bill_year, bill_month);
-- ALTER TABLE r_realty_we ADD INDEX idx_realty_we_realtySerial(realty_serial);
-- ALTER TABLE r_realty_we ADD INDEX idx_realty_we_bill_id(we_bill_id);


DROP TABLE IF EXISTS s_system_setting;
CREATE TABLE s_system_setting(
    name            varchar(30)       not null     comment '参数名',
    `value`         varchar(50)       comment '参数值',
    modified_time   datetime          not null    comment '修改时间',
    primary key(name)
);

DROP TABLE IF EXISTS s_penalty_setting;
create table s_penalty_setting(
    id             bigint unsigned   not null   auto_increment,
    bill_year_month  varchar(10)       not null   comment '账单年月',
    bill_type      smallint unsigned not null   default 0  comment '类别 1 物业；2 冷藏',
    penalty_start_date  date         comment '滞纳金起征日期',
    penalty_cal_date    date         comment '滞纳金起算日期',
    is_penalty_free     tinyint(1) unsigned not null default 0 comment '免征滞纳金',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key(id),
    unique key uk_penalty_setting_time_type(bill_year_month, bill_type)
);


drop table if exists r_realty_receipt;
create table r_realty_receipt
(
    id              bigint unsigned primary key auto_increment comment 'id',
    title           varchar(50)                 not null comment '标题',
    header_category tinyint unsigned default 0  not null comment '类型(1:企业;2:个人)',
    apply_status    tinyint          default 0 comment '审核状态(0:待审核;1:审核通过)',
    apply_amount    decimal(12, 2)              not null comment '申请金额',
    apply_man       bigint unsigned             null comment '申请人',
    apply_user_name varchar(255) comment '申请人姓名',
    apply_time      datetime                    not null comment '申请时间',
    audit_man       bigint unsigned             null comment '审核人',
    audit_time      datetime                    null comment '审核时间',
    audit_remark    varchar(255)     default '' not null comment '审核备注',
    send            tinyint          default 0  null comment '下发',
    is_disabled     tinyint(1)                  not null default 0 comment '禁用'
);

drop table if exists r_realty_receipt_item;
create table r_realty_receipt_item
(
    realty_receipt_id bigint unsigned not null comment '物业发票id',
    receipt_serial_no varchar(20)     not null comment '发票单据号',
    primary key (realty_receipt_id, receipt_serial_no)
);

DROP TABLE IF EXISTS r_realty_energy_point;
CREATE TABLE r_realty_energy_point
(
    point_code       varchar(20)                  not null comment '设备编码',
    point_type       tinyint unsigned default '0' not null comment '设备类型 1 电；2 水',
    realty_serial_no varchar(30)      default ''  not null comment '物业编号',
    primary key (point_code, point_type, realty_serial_no)
);

DROP TABLE IF EXISTS r_realty_bill_receipt;
CREATE TABLE r_realty_bill_receipt
(
    bill_id          bigint unsigned not null comment '账单id',
    receipt_apply_id bigint unsigned not null comment '发票申请id',
    primary key (bill_id, receipt_apply_id)
);

/* 广告位 */
DROP TABLE IF EXISTS r_advertising_space;
CREATE TABLE r_advertising_space(
    id             bigint unsigned    not null    auto_increment,
    serial_no      varchar(30)        not null    default '' comment '编号',
    name           varchar(60)        not null    default '' comment '广告位名',
    region         varchar(100)       comment '区域',
    street         varchar(100)       comment '街道',
    address        varchar(150)       comment '广告位置',
    length         decimal(6, 2)      comment '长',
    width          decimal(6, 2)      comment '宽',
    size           decimal(8, 2)      comment '面积',
    is_disabled    tinyint(1)         not null   default 0  comment '禁用',
    creator_id     bigint unsigned    not null   default 0  comment '创建人id',
    creator_name   varchar(30)        not null   default '' comment '创建人姓名',
    create_time    datetime           not null   comment '创建时间',
    modifier_id    bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name  varchar(30)        not null   default '' comment '修改人姓名',
    modified_time  datetime           not null   comment '修改时间',
    primary key (id),
    unique key uk_advertising_space_serial(serial_no)
);

DROP TABLE IF EXISTS r_advertising_space_position;
CREATE TABLE r_advertising_space_position(
    id             bigint unsigned    not null   auto_increment,
    space_id       bigint unsigned    not null   default 0  comment '广告位id',
    region_id      bigint unsigned    not null    default 0  comment '区域id',
    region_name    varchar(60)        comment '区域名',
    street_id      bigint unsigned    not null   default 0  comment '街道id',
    street_name    varchar(60)        comment '街道名',
    is_disabled    tinyint(1)         not null   default 0  comment '禁用',
    creator_id     bigint unsigned    not null   default 0  comment '创建人id',
    creator_name   varchar(30)        not null   default '' comment '创建人姓名',
    create_time    datetime           not null   comment '创建时间',
    modifier_id    bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name  varchar(30)        not null   default '' comment '修改人姓名',
    modified_time  datetime           not null   comment '修改时间',
    primary key (id),
    unique key uk_advertising_space_position(space_id, region_id, street_id)
);

DROP TABLE IF EXISTS r_advertising_contract;
CREATE TABLE r_advertising_contract(
    id             bigint unsigned    not null   auto_increment,
    contract_no    varchar(30)        not null   default '' comment '合同编号',
    space_id       bigint unsigned    not null   default 0  comment '广告位id',
    customer_name  varchar(60)        not null   default '' comment '客户名',
    customer_user    varchar(20)      comment '客户联系人',
    customer_contact varchar(20)      comment '客户联系方式',
    present_months tinyint unsigned   not null   default 0  comment '赠送月份',
    rent_months    tinyint unsigned   not null   default 0  comment '租赁月份数',
    sign_date      date               not null   comment '签约日期',
    start_date     date               not null   comment '开始日期',
    end_date       date               not null   comment '结束日期',
    amount         decimal(8, 2)      not null   default 0  comment '合同金额',
    cost           decimal(8, 2)      not null   default 0  comment '成本',
    status         tinyint(4) unsigned not null  default 0  comment '状态, 0停用;1未生效;2启用',
    stop_by        bigint unsigned    not null   comment '停用人',
    stop_time      datetime           comment '停用时间',
    remark         varchar(200)       comment '备注',
    is_paid        tinyint(1) unsigned not null  default 0  comment '已支付',
    is_disabled    tinyint(1)         not null   default 0  comment '禁用',
    creator_id     bigint unsigned    not null   default 0  comment '创建人id',
    creator_name   varchar(30)        not null   default '' comment '创建人姓名',
    create_time    datetime           not null   comment '创建时间',
    modifier_id    bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name  varchar(30)        not null   default '' comment '修改人姓名',
    modified_time  datetime           not null   comment '修改时间',
    primary key (id),
    unique key uk_advertising_contract_no(contract_no)
);
-- ALTER TABLE r_advertising_contract ADD INDEX idx_advertising_contract_space(space_id);
-- ALTER TABLE r_advertising_contract ADD INDEX idx_advertising_contract_startDate(start_date);
-- ALTER TABLE r_advertising_contract ADD INDEX idx_advertising_contract_endDate(end_date);

DROP TABLE IF EXISTS r_advertising_profit_share;
CREATE TABLE r_advertising_profit_share(
    id             bigint unsigned    not null   auto_increment,
    contract_id    bigint unsigned    not null   default 0  comment '合同id',
    customer_id    bigint unsigned    not null   default 0  comment '客户id',
    customer_name  varchar(60)        comment '客户名',
    realty_serial  varchar(30)        not null   default '' comment '物业编号',
    realty_name    varchar(60)        comment '客户名',
    share_amount   decimal(8, 2)      not null   default 0  comment '分享金额',
    remark         varchar(200)       comment '备注',
    is_disabled    tinyint(1)         not null   default 0  comment '禁用',
    creator_id     bigint unsigned    not null   default 0  comment '创建人id',
    creator_name   varchar(30)        not null   default '' comment '创建人姓名',
    create_time    datetime           not null   comment '创建时间',
    modifier_id    bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name  varchar(30)        not null   default '' comment '修改人姓名',
    modified_time  datetime           not null   comment '修改时间',
    primary key (id),
    unique key uk_advertising_profit_share(contract_id, realty_serial, customer_id)
);

DROP TABLE IF EXISTS r_advertising_media;
CREATE TABLE r_advertising_media(
    id             bigint unsigned    not null   auto_increment,
    contract_id    bigint unsigned    not null   default 0  comment '合同id',
    media_url      varchar(200)       comment '媒体url',
    is_disabled    tinyint(1)         not null   default 0  comment '禁用',
    creator_id     bigint unsigned    not null   default 0  comment '创建人id',
    creator_name   varchar(30)        not null   default '' comment '创建人姓名',
    create_time    datetime           not null   comment '创建时间',
    modifier_id    bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name  varchar(30)        not null   default '' comment '修改人姓名',
    modified_time  datetime           not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE r_advertising_media ADD INDEX idx_advertising_media_contract(contract_id);


DROP TABLE IF EXISTS r_advertising_bill;
CREATE TABLE r_advertising_bill(
    id              bigint unsigned    not null   auto_increment,
    bill_year       smallint unsigned  not null   comment '年份',
    bill_month      tinyint unsigned   not null   comment '月份',
    space_id        bigint unsigned    not null   comment '广告位id',
    contract_no     varchar(30)        not null   comment '合同编号',
    amount          decimal(10, 2)     not null   default 0  comment '金额',
    paid_amount     decimal(10, 2)     not null   default 0  comment '支付金额',
    paid_time       datetime           comment '支付时间',
    remote_order_id bigint unsigned    not null   default 0  comment '订单id',
    status          tinyint unsigned   not null   default 0  comment '账单状态：0初始化；1已支付',
    toll_man_id     bigint unsigned    not null   default 0  comment '收费员',
    toll_serial     varchar(10)        comment '票据号',
    remark          varchar(200)       comment '备注',
    is_receipt      tinyint(1)         not null   default 0  comment '是否开票',
    receipt_title   varchar(100)       comment '开票抬头',
    receipt_time    datetime           comment '开票时间',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null   comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE r_advertising_bill ADD INDEX idx_advertising_bill_year_month(bill_year, bill_month);
-- ALTER TABLE r_advertising_bill ADD INDEX idx_advertising_bill_space(space_id);
-- ALTER TABLE r_advertising_bill ADD INDEX idx_advertising_bill_contract(contract_no);

DROP TABLE IF EXISTS r_advertising_payoff;
CREATE TABLE r_advertising_payoff(
    id              bigint unsigned    not null   auto_increment,
    bill_year       smallint unsigned  not null   comment '年份',
    bill_month      tinyint unsigned   not null   comment '月份',
    space_id        bigint unsigned    not null   comment '广告位id',
    contract_no     varchar(30)        not null   comment '合同编号',
    customer_id     bigint unsigned    not null   default 0  comment '客户id',
    customer_name   varchar(60)        comment '客户名',
    realty_serial   varchar(30)        not null   default '' comment '物业编号',
    realty_name     varchar(60)        comment '物业名',
    amount          decimal(10, 2)     not null   default 0  comment '金额',
    status          tinyint unsigned   not null   default 0  comment '账单状态：0初始化；1已支付',
    toll_man_id     bigint unsigned    not null   default 0  comment '收费员',
    payway          smallint unsigned  not null   default 0  comment '支付方式',
    paid_time       datetime          comment '支付时间',
    remark          varchar(255)      comment '备注',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null   comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE r_advertising_payoff ADD INDEX idx_advertising_payoff_year_month(bill_year, bill_month);
-- ALTER TABLE r_advertising_payoff ADD INDEX idx_advertising_payoff_space(space_id);
-- ALTER TABLE r_advertising_payoff ADD INDEX idx_advertising_payoff_contract(contract_no);


DROP TABLE IF EXISTS r_energy_consume_unit;
CREATE TABLE r_energy_consume_unit(
    id              bigint unsigned    not null   auto_increment,
    unit            varchar(10)        not null   default '' comment '能源消费单位',
    type            tinyint unsigned   not null   default 0  comment '能源类别，1水；2电',
    name            varchar(100)       not null   default '' comment '能源消费单位名称',
    remark          varchar(200)       comment '备注',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null   comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null   comment '修改时间',
    primary key (id),
    unique key uk_energy_consume_unit(`unit`)
);

-- 能源损益
DROP TABLE IF EXISTS r_energy_profit;
CREATE TABLE r_energy_profit(
    id              bigint unsigned    not null   auto_increment,
    bill_time       varchar(10)        not null   comment '年月',
    energy_type     tinyint unsigned   not null   default 0  comment '能源类别，1水；2电',
    recorded_start_date date           not null   comment '记录抄表时间起',
    recorded_end_date   date           not null   comment '记录抄表时间止',
    recorded_days       smallint unsigned   not null   default 0  comment '记录间隔天数',
    recorded_cost       int unsigned   not null   default 0  comment '记录能源实耗',
    balance_start_date  date           comment '结算抄表时间起',
    balance_end_date    date           comment '结算抄表时间止',
    balance_days    smallint unsigned not null   default 0  comment '结算间隔天数',
    balance_cost    int unsigned      not null   default 0  comment '结算能源实耗',
    diff_days       smallint          not null   default 0  comment '抄表差异天数',
    diff_cost       int               not null   default 0  comment '抄表差异耗费',
    profits_cost    int unsigned      not null   default 0  comment '能源损益量',
    profits_rate    decimal(6, 2)     not null   default 0  comment '能源损益率',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    primary key (id),
    unique key uk_energy_profit(`bill_time`, `energy_type`)
);

-- 能源损益明细
DROP TABLE IF EXISTS r_energy_profit_item;
CREATE TABLE r_energy_profit_item(
    id              bigint unsigned    not null   auto_increment,
    profit_id       bigint unsigned    not null   default 0 comment '损益id',
    bill_year       smallint unsigned  not null   comment '年份',
    bill_month      tinyint unsigned   not null   comment '月份',
    energy_type     tinyint unsigned   not null   default 0  comment '能源类别，1水；2电',
    source          tinyint unsigned   not null   default 0 comment '损益数据来源 1 系统；2 第三方',
    unit_key        varchar(10)        comment '能源消费单位key',
    unit_name       varchar(100)       comment '能源消费单位名',
    cost            int unsigned       not null   default 0 comment '能源消耗量',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null   comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null   comment '修改时间',
    primary key (id),
    unique key uk_energy_profit_item(bill_year, bill_month, energy_type, unit_key, unit_name)
);
-- ALTER TABLE r_energy_profit_item ADD INDEX idx_energy_profit_item_profitId(`profit_id`);


-- 商铺消防安全档案
DROP TABLE IF EXISTS r_firefighting_file;
CREATE TABLE r_firefighting_file(
    id              bigint unsigned    not null   auto_increment,
    store           varchar(50)        not null   default '' comment '店铺名',
    enterprise_id    bigint unsigned    not null   default 0  comment '经营户id',
    store_keyman    varchar(30)        not null   default '' comment '店铺负责人',
    store_contact   varchar(20)        comment '店铺联系电话',
    store_address   varchar(200)       comment '店铺地址',
    building_area   decimal(8, 2)      comment '店铺建筑面积',
    renting_area    decimal(8, 2)      comment '店铺出租面积',
    renting_floor   tinyint            comment '店铺出租层数',
    product_type    varchar(10)        not null   default '' comment '店铺生产性质',
    is_business_license_issued tinyint not null   default 0 comment '工商营业执照办理情况 1 有 0 无',
    accommodated   varchar(30)          not null   default '' comment '住人情况',
    flame_cooking  varchar(30)         not null   default '' comment '明火煮食',
    heater_equiped varchar(30)          not null   default '' comment '热水器',
    fire_extinguisher smallint unsigned not null  default 0 comment '灭火器',
    is_fire_alarms_disposed       tinyint  not null  default 0 comment '火警报警器 1 有 0 无',
    is_fire_reels_disposed        tinyint  not null  default 0 comment '消防卷盘 1 有 0 无',
    is_simple_sprinklers_disposed tinyint  not null  default 0 comment '简易喷淋 1 有 0 无',
    is_emergency_lights_disposed  tinyint  not null  default 0 comment '应急灯 1 有 0 无',
    evacuration_stairs    varchar(10)  default '' comment '疏散楼梯',
    is_room_separated     tinyint      default 0  comment '房间分隔 1 有 0 无',
    is_escape_hatch_disposed      tinyint  not null  default 0 comment '逃生出口 1 有 0 无',
    distribution_lines    varchar(10)             default '' comment '配电线路',
    other_surroundings    varchar(200)            comment '其他消防环境',
    emergency_stairs     varchar(100)      not null  default ''  comment '逃生梯',
    expire_fire_extinguisher  int unsigned      not null  default 0  comment '过期灭火器数量',
    gas_pipe  varchar(30)      not null  default ''  comment '煤气管',
    valve  varchar(30)      not null  default ''  comment '阀门',
    inspect_result  smallint  not null   default 0  comment '巡检结果 0 未处理 1 通过 99 不通过 101 复检通过 199 复检不通过',
    inspect_date            date             not null         comment '巡检日期',
    rectification_deadline  date                              comment '整改截止日期',
    re_unauthorized_residence  varchar(100)      not null  default ''  comment '整改违规住人情况',
    re_fire_extinguisher  varchar(100)      not null  default ''  comment '整改灭火器情况',
    re_fire_extinguisher_num  int      unsigned      not null  default 0  comment '整改灭火器数量',
    re_emergency_lights  varchar(100)      not null  default ''  comment '整改应急灯情况',
    re_thoroughfare  varchar(100)      not null  default ''  comment '整改消防通道情况',
    re_wire  varchar(100)      not null  default ''  comment '整改电线情况',
    re_heater_equiped  varchar(100)      not null  default ''  comment '整改热水器情况',
    re_emergency_stairs  varchar(100)      not null  default ''  comment '整改逃生梯情况',
    re_smoke_detector  varchar(100)      not null  default ''  comment '整改烟感器情况',
    re_gas_pipe  varchar(100)      not null  default ''  comment '整改煤气管情况',
    re_valve  varchar(100)      not null  default ''  comment '整改阀门情况',
    reinspect_opinion     varchar(200)                comment '复查意见',
    reinspector           varchar(200)                comment '复查人',
    reinspect_date        date                        comment '复查日期',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null   comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null   comment '修改时间',
    primary key(id)
);

DROP TABLE IF EXISTS r_firefighting_file_detail;
CREATE TABLE r_firefighting_file_detail(
    file_id         bigint unsigned    not null     comment '档案id',
    venue_description text             comment '场所说明',
    follow_up       varchar(200)       comment '情况跟踪',
    modified_time   datetime           not null   comment '修改时间',
    primary key (file_id)
);

DROP TABLE IF EXISTS r_firefighting_utility;
CREATE TABLE r_firefighting_utility(
    id              bigint unsigned    not null   auto_increment,
    region_id       bigint unsigned    not null   default 0  comment '区域id',
    region_name     varchar(60)                              comment '区域名',
    street_id       bigint unsigned    not null   default 0  comment '街道id',
    street_name     varchar(60)                              comment '街道名',
    location        varchar(100)                             comment '位置',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null              comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null              comment '修改时间',
    primary key(id),
    unique key uk_firefighting_utility(region_name, street_name, location)
);
-- ALTER TABLE r_firefighting_utility ADD INDEX idx_firefighting_utility_region_street(region_id, street_id);

DROP TABLE IF EXISTS r_firefighting_utility_inspection;
CREATE TABLE r_firefighting_utility_inspection(
    id              bigint unsigned    not null   auto_increment,
    utility_id      bigint unsigned    not null   default 0  comment '公共消防设施id',
    inspect_date    date               not null              comment '巡检日期',
    fire_hydrant    varchar(20)                              comment '消防栓',
    fire_hose       varchar(20)                              comment '消防水带',
    fire_extinguisher   varchar(20)                          comment '灭火器',
    water_gun           varchar(20)                          comment '水枪',
    valve           varchar(20)                              comment '阀门',
    fire_exits      varchar(20)                              comment '消防通道',
    fire_extinguisher_num  int unsigned      not null  default 0  comment '灭火器数量',
    expire_fire_extinguisher  int unsigned      not null  default 0  comment '过期灭火器数量',
    remark          varchar(200)                             comment '备注',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null              comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null              comment '修改时间',
    primary key(id)
);
-- ALTER TABLE r_firefighting_utility_inspection ADD INDEX idx_firefighting_utility_inspection_utilityId(utility_id);
-- ALTER TABLE r_firefighting_utility_inspection ADD INDEX idx_firefighting_utility_inspection_inspectDate(inspect_date);

DROP TABLE IF EXISTS r_firefighting_template;
CREATE TABLE r_firefighting_template(
    id              bigint unsigned    not null   auto_increment,
    code            varchar(20)        not null   default '' comment '模板编码',
    version         int unsigned       not null   default 0  comment '模板版本号',
    status          tinyint            not null   default 0  comment '模板状态 0 初始 -1 失效 1 生效',
    valid_date      date               comment '生效日期',
    invalid_date    date               comment '失效日期',
    title           varchar(100)       comment '标题',
    content         text               comment '内容',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null   comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null   comment '修改时间',
    primary key(id),
    unique key uk_firefighting_template_code_version(code, version)
);

DROP TABLE IF EXISTS r_firefighting_template_variables;
CREATE TABLE r_firefighting_template_variables(
    code            varchar(20)        not null   default '' comment '模板编码',
    version         int unsigned       not null   default 0  comment '模板版本号',
    attr_name       varchar(50)        not null   default '' comment '模板变量名',
    attr_type       varchar(50)                   comment '模板变量类型',
    modified_time   datetime           not null   comment '修改时间',
    primary key (code, version, attr_name)
);

DROP TABLE IF EXISTS r_firefighting_form_template;
CREATE TABLE r_firefighting_form_template(
    id              bigint unsigned    not null   auto_increment,
    form            varchar(50)        not null   comment '表单',
    form_attr       varchar(50)        not null   comment '表单属性',
    template_code   varchar(20)        not null   comment '模板',
    template_version int unsigned      not null   comment '版本',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null   comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null   comment '修改时间',
    primary key(id),
    unique key uk_firefighting_form_template (form, form_attr)
);


DROP TABLE IF EXISTS r_firefighting_notice;
CREATE TABLE r_firefighting_notice(
    id               bigint unsigned    not null   auto_increment,
    template_code    varchar(20)        not null   default '' comment '模板编码',
    template_version int unsigned       not null   default 0  comment '模板版本号',
    store            varchar(200)                  comment '单位地址',
    enterprise_id    bigint unsigned    not null   default 0  comment '经营户id',
    store_keyman     varchar(200)       not null   comment '负责人',
    store_contact    varchar(20)                   comment '负责人联系方式',
    inspector        varchar(200)                  comment '巡检员',
    notify_date      date               not null   comment '通知日期',
    is_disabled      tinyint(1)         not null   default 0  comment '禁用',
    creator_id       bigint unsigned    not null   default 0  comment '创建人id',
    creator_name     varchar(30)        not null   default '' comment '创建人姓名',
    create_time      datetime           not null   comment '创建时间',
    modifier_id      bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name    varchar(30)        not null   default '' comment '修改人姓名',
    modified_time    datetime           not null   comment '修改时间',
    primary key(id)
);
--alter table r_firefighting_notice add index idx_firefighting_notice_notify_date(notify_date);


DROP TABLE IF EXISTS r_firefighting_store_inspection;
CREATE TABLE r_firefighting_store_inspection(
    id              bigint unsigned    not null   auto_increment,
    inspect_agency        varchar(100)                       comment '检查单位',
    enterprise_id         bigint unsigned not null default 0 comment '经营户id',
    store                 varchar(200)                       comment '商铺',
    store_business_type   varchar(20)                        comment '商铺经营类别',
    store_owner           varchar(30)                        comment '商铺业主',
    store_owner_contact   varchar(20)                        comment '商铺业主联系方式',
    store_runner          varchar(30)                        comment '商铺经营者',
    store_runner_contact  varchar(20)                        comment '商铺经营者联系方式',
    is_store_accommodated    tinyint   not null   default 0  comment '违规住人 1 是 0 否',
    store_accommodated_count smallint  not null   default 0  comment '居住人数',
    is_firefighting_facilities_satisfied tinyint  default 0  comment '消防设施配齐 1 是 0 否',
    is_firefighting_wall_satisfied       tinyint  default 0  comment '砖墙保护 1 是 0 否',
    is_firefighting_stairs_satisfied     tinyint  default 0  comment '楼梯保护 1 是 0 否',
    is_firefighting_lines_satisfied      tinyint  default 0  comment '配电线路保护 1 是 0 否',
    is_firefighting_alarm_satisfied      tinyint  default 0  comment '火灾报警器配齐 1 是 0 否',
    is_firefighting_exit_satisfied       tinyint  default 0  comment '安全出口保护 1 是 0 否',
    inspect_result           smallint    not null default 0  comment '巡检结果 0 未处理 1 通过 99 不通过 101 复检通过 199 复检不通过',
    inspect_opinions         varchar(500)                    comment '巡检意见',
    rectification_time_limit smallint             default 0  comment '整改期限',
    rectification_deadline   date                            comment '整改截止日期',
    inspect_date             date        not null            comment '巡检日期',
    inspected_signature    varchar(200)                      comment '被巡检单位负责人签名',
    inspector              varchar(200)                      comment '巡查人员',
    inspector_assistant    varchar(30)                       comment '巡查服务人员',
    store_keyman_signature varchar(200)                      comment '被巡察场所负责人签名',
    reinspect_opinions     varchar(500)                      comment '复查意见',
    reinspector            varchar(200)                      comment '复查人员',
    reinspect_date         date                              comment '复查日期',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null   comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null   comment '修改时间',
    primary key(id)
);

DROP TABLE IF EXISTS r_firefighting_small_places_inspection;
CREATE TABLE r_firefighting_small_places_inspection(
    id               bigint unsigned    not null   auto_increment,
    inspected_place  varchar(100)       not null              comment '受检单位',
    enterprise_id    bigint unsigned    not null   default 0  comment '经营户id',
    keyman           varchar(30)        not null              comment '负责人',
    address          varchar(200)                             comment '地址',
    contact          varchar(30)                              comment '联系方式',
    place_type       varchar(20)        not null              comment '场所类型',
    business_license varchar(10)        not null              comment '工商注册情况',
    building_safety  varchar(10)        not null              comment '建筑结构安全情况',
    business_scope   varchar(100)                             comment '生产经营范围',
    building_count   smallint unsigned                        comment '建筑物数量',
    building_floors  smallint unsigned                        comment '最高建筑层数',
    building_area    decimal(10, 2)                           comment '建筑占地面积',
    building_structure        varchar(20)                     comment '建筑结构',
    building_structure_other  varchar(100)                    comment '建筑结构其他',
    building_usage   varchar(50)                              comment '建筑使用情况',
    runner_usage_area decimal(10, 2)                          comment '经营者使用面积',
    runner_staffs     smallint unsigned                       comment '员工数量',
    evacuration_staris varchar(10)                            comment '疏散出口楼梯',
    evacuration_roof_straight varchar(10)                     comment '疏散出口直通天面',
    evacuration_exit   varchar(10)                            comment '疏散安全出口',
    evacuration_escape_window varchar(10)                     comment '疏散逃生窗口',
    fire_extinguisher  smallint unsigned           default 0  comment '灭火器',
    emergency_lights   smallint unsigned           default 0  comment '应急照明',
    is_fire_reels_disposed        tinyint          default 0  comment '消防卷盘 1 有 0 无',
    is_escape_facilities_disposed tinyint          default 0  comment '紧急逃生措施 1 有 0 无',
    is_simple_sprinklers_disposed tinyint          default 0  comment '简易喷淋 1 有 0 无',
    is_fire_alarms_disposed       tinyint          default 0  comment '火警报警器 1 有 0 无',
    expire_fire_extinguisher  int unsigned      not null  default 0  comment '过期灭火器数量',
    unauthorized_residence  varchar(30)      not null  default ''  comment '违规住人',
    re_fire_extinguisher  varchar(30)      not null  default ''  comment '整改灭火器情况',
    re_fire_extinguisher_num  int unsigned      not null  default 0  comment '整改灭火器数量',
    re_emergency_lights  varchar(30)      not null  default ''  comment '整改应急灯情况',
    re_fire_alarms_disposed  varchar(30)      not null  default ''  comment '整改消防报警器情况',
    re_wire  varchar(30)      not null  default ''  comment '整改电线情况',
    re_unauthorized_residence  varchar(30)      not null  default ''  comment '整改违规住人情况',
    inspect_result   smallint           not null   default 0  comment '巡检结果 0 未处理 1 通过 99 不通过 101 复检通过 199 复检不通过',
    inspector        varchar(200)                             comment '巡检人员',
    inspect_date     date               not null              comment '巡检日期',
    keyman_inspected_signature   varchar(200)                 comment '被检查场所负责人签名',
    rectification_deadline   date                             comment '整改截止日期',
    reinspector      varchar(200)                             comment '复查人员',
    reinspect_date   date                                     comment '复查日期',
    keyman_reinspected_signature varchar(200)                 comment '被检查场所复查负责人签名',
    is_disabled      tinyint(1)         not null   default 0  comment '禁用',
    creator_id       bigint unsigned    not null   default 0  comment '创建人id',
    creator_name     varchar(30)        not null   default '' comment '创建人姓名',
    create_time      datetime           not null   comment '创建时间',
    modifier_id      bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name    varchar(30)        not null   default '' comment '修改人姓名',
    modified_time    datetime           not null   comment '修改时间',
    primary key(id)
);

DROP TABLE IF EXISTS r_firefighting_small_places_inspection_detail;
CREATE TABLE r_firefighting_small_places_inspection_detail(
    inspection_id    bigint unsigned     not null      comment '巡检id',
    floor1_usage     varchar(50)                       comment '1层用途',
    floor2_usage     varchar(50)                       comment '2层用途',
    floor3_usage     varchar(50)                       comment '3层用途',
    floor4_usage     varchar(50)                       comment '4层用途',
    floor5_usage     varchar(50)                       comment '5层用途',
    floor6_usage     varchar(50)                       comment '6层用途',
    floor_others_usage     varchar(50)                 comment '其他层用途',
    floor1_runner_usage    varchar(50)                 comment '经营者1层用途',
    floor2_runner_usage    varchar(50)                 comment '经营者2层用途',
    floor3_runner_usage    varchar(50)                 comment '经营者3层用途',
    floor4_runner_usage    varchar(50)                 comment '经营者4层用途',
    floor5_runner_usage    varchar(50)                 comment '经营者5层用途',
    floor6_runner_usage    varchar(50)                 comment '经营者6层用途',
    floor_others_runner_usage     varchar(50)          comment '经营者其他层用途',
    remark           varchar(200)                      comment '备注',
    inspect_opinion  varchar(200)                      comment '检查意见',
    inspect_opinion_code     varchar(20)               comment '检查意见模板',
    inspect_opinion_version  int unsigned              comment '检查意见模板版本',
    reinspect_opinion           varchar(200)           comment '复查意见',
    reinspect_opinion_code      varchar(20)            comment '复查意见模板',
    reinspect_opinion_version   int unsigned           comment '检查意见模板版本',
    store_rectification         varchar(200)           comment '小商铺整改意见',
    store_rectification_code    varchar(20)            comment '小商铺整改意见模板',
    store_rectification_version int unsigned           comment '小商铺整改意见模板版本',
    workshop_rectification      varchar(200)           comment '小作坊整改意见',
    workshop_rectification_code varchar(20)            comment '小作坊整改意见模板',
    workshop_rectification_version int unsigned        comment '小作坊整改意见模板版本',
    rental_rectification        varchar(200)           comment '出租屋整改意见',
    rental_rectification_code   varchar(20)            comment '出租屋整改意见模板',
    rental_rectification_version   int unsigned        comment '出租屋整改意见模板版本',
    modified_time    datetime            not null      comment '修改时间',
    primary key (inspection_id)
);

DROP TABLE IF EXISTS r_firefighting_inspection_attr;
CREATE TABLE r_firefighting_inspection_attr(
    id               bigint unsigned     not null   auto_increment,
    inspection_type  varchar(20)         not null      comment '巡检类型',
    inspection_id    bigint unsigned     not null      comment '巡检id',
    template_code    varchar(20)         not null      comment '模板编码',
    template_version int unsigned        not null      comment '模板版本',
    attr_name        varchar(50)         not null      comment '变量',
    attr_type        varchar(50)                       comment '变量类型',
    attr_value       varchar(200)                      comment '变量值',
    modified_time    datetime            not null      comment '修改时间',
    primary key (id),
    unique key uk_firefighing_inspection_attr(inspection_id, template_code, template_version, attr_name)
);

DROP TABLE IF EXISTS r_firefighting_accommodate_inspection;
CREATE TABLE r_firefighting_accommodate_inspection(
    id               bigint unsigned     not null   auto_increment,
    address          varchar(200)                             comment '地址',
    business_type    varchar(20)                              comment '经营类别',
    enterprise_id    bigint unsigned     not null   default 0 comment '企业id',
    accommodated     tinyint unsigned    not null   default 0 comment '违规住人',
    accommodated_count smallint unsigned not null   default 0 comment '居住人数',
    runner           varchar(30)                              comment '经营者',
    runner_contact   varchar(20)                              comment '经营者联系方式',
    is_fire_doors_disqualified      tinyint unsigned          comment '无防火门',
    is_evacuration_disqualified     tinyint unsigned          comment '无逃生梯',
    is_fire_facilities_disqualified tinyint unsigned          comment '无消防器材',
    is_fire_exit_disqualified       tinyint unsigned          comment '无消防通道',
    is_electric_lines_disqualified  tinyint unsigned          comment '电线乱拉',
    is_smoke_detector_disqualified  tinyint unsigned          comment '无烟感器',
    is_sprinkler_disqualified       tinyint unsigned          comment '无自动喷淋',
    inspect_opinions   varchar(500)                           comment '巡检意见',
    inspect_opinion_code    varchar(20)                       comment '巡检意见模板',
    inspect_opinion_version int unsigned                      comment '巡检意见版本',
    inspect_result     smallint         not null   default 0  comment '巡检结果 0 未处理 1 通过 99 不通过 101 复检通过 199 复检不通过',
    inspector          varchar(200)                           comment '巡查人员',
    inspect_date       date             not null              comment '巡检日期',
    inspect_unit            varchar(50)                       comment '被巡检单位',
    inspected_signature     varchar(200)                      comment '被巡检单位负责人签名',
    rectification_deadline  date                              comment '整改截止日期',
    rectification           varchar(500)                      comment '整改意见',
    rectification_code      varchar(20)                       comment '整改意见模板',
    rectification_version   int unsigned                      comment '整改意见版本',
    rectification_supervisor varchar(200)                     comment '整改监督人',
    rectification_signature  varchar(200)                     comment '被整改负责人签名',
    reinspect_opinions varchar(500)                           comment '复查意见',
    reinspector        varchar(200)                           comment '复查人',
    reinspect_date     date                                   comment '整改日期',
    is_disabled      tinyint(1)         not null   default 0  comment '禁用',
    creator_id       bigint unsigned    not null   default 0  comment '创建人id',
    creator_name     varchar(30)        not null   default '' comment '创建人姓名',
    create_time      datetime           not null              comment '创建时间',
    modifier_id      bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name    varchar(30)        not null   default '' comment '修改人姓名',
    modified_time    datetime           not null              comment '修改时间',
    primary key(id)
);

DROP TABLE IF EXISTS r_firefighting_inspect_media;
CREATE TABLE r_firefighting_inspect_media(
    id               bigint unsigned    not null    auto_increment,
    inspect_id       bigint unsigned    not null    default 0 comment '巡检id',
    inspect_type     varchar(20)        not null              comment '巡检类型',
    inspect_times    smallint unsigned  not null    default 0 comment '巡检次数',
    media_url        varchar(200)                             comment '多媒体url',
    modified_time    datetime           not null              comment '修改时间',
    primary key(id)
);
-- ALTER TABLE r_firefighting_inspect_media ADD INDEX idx_firefighting_inspect_media(inspect_id, inspect_type);

DROP TABLE IF EXISTS r_firefighting_inspect_realty;
CREATE TABLE r_firefighting_inspect_realty(
    id               bigint unsigned    not null    auto_increment,
    inspect_id       bigint unsigned    not null    default 0 comment '巡检id',
    inspect_type     varchar(20)        not null              comment '巡检类型',
    realty_serial    varchar(30)                              comment '物业编号',
    modified_time    datetime           not null              comment '修改时间',
    primary key(id),
    unique key uk_firefighting_inspect_realty(inspect_id, inspect_type, realty_serial)
);

DROP TABLE IF EXISTS r_firefighting_inspect_task;
CREATE TABLE r_firefighting_inspect_task(
    id               bigint unsigned    not null   auto_increment,
    name             varchar(100)       not null              comment '任务',
    category         tinyint unsigned   not null              comment '1 公共：2 物业',
    start_date       date               not null              comment '任务开始时间',
    end_date         date                                     comment '任务结束时间',
    remark           varchar(200)                             comment '备注',
    is_disabled      tinyint(1)         not null   default 0  comment '禁用',
    creator_id       bigint unsigned    not null   default 0  comment '创建人id',
    creator_name     varchar(30)        not null   default '' comment '创建人姓名',
    create_time      datetime           not null              comment '创建时间',
    modifier_id      bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name    varchar(30)        not null   default '' comment '修改人姓名',
    modified_time    datetime           not null              comment '修改时间',
    primary key(id)
);

DROP TABLE IF EXISTS r_firefighting_inspect_task_item;
CREATE TABLE r_firefighting_inspect_task_item(
   id               bigint unsigned    not null   auto_increment,
   task_id          bigint unsigned    not null               comment '任务id',
   utility_id       bigint unsigned    not null   default 0   comment '公共消防设施id',
   enterprise_id    bigint unsigned    not null   default 0   comment '经营户id',
   realty_serial    varchar(30)        not null   default ''  comment '物业编号',
   inspect_type     varchar(50)        not null   default ''  comment '巡检类型',
   inspect_id       bigint unsigned    not null   default 0   comment '巡检id',
   modified_time    datetime           not null               comment '修改时间',
   primary key(id),
   unique key uk_firefighting_inspection_task_item(task_id, utility_id, enterprise_id, realty_serial, inspect_type)
);


DROP TABLE IF EXISTS r_firefighting_inspect_task_realty;
CREATE TABLE r_firefighting_inspect_task_realty(
    task_id          bigint unsigned    not null               comment '任务id',
    task_item_id     bigint unsigned    not null               comment '子任务id',
    realty_serial    varchar(30)        not null   default ''  comment '物业编号',
    modified_time    datetime           not null               comment '修改时间',
    primary key (task_id, task_item_id, realty_serial)
);

DROP TABLE IF EXISTS r_realty_statistics;
CREATE TABLE r_realty_statistics(
    id              bigint unsigned   not null   auto_increment,
    statistics_date       date not null   comment '统计日期',
    realty_num bigint unsigned NOT NULL DEFAULT '0' comment '物业数量',
    un_rent_company_realty_num bigint unsigned NOT NULL DEFAULT '0' comment '未租赁公司物业数量',
    rent_company_realty_num bigint unsigned NOT NULL DEFAULT '0' comment '已租赁公司物业数量',
    realty_contract_num bigint unsigned NOT NULL DEFAULT '0' comment '物业合同数量',
    rent_contract_num bigint unsigned NOT NULL DEFAULT '0' comment '代租合同数量',
    rent_collect_num   bigint unsigned NOT NULL DEFAULT '0' comment '已收租数量',
    rent_collect_amount   decimal(10, 2)    not null   default 0 comment '已收租金额',
    un_rent_collect_num   bigint unsigned NOT NULL DEFAULT '0' comment '未已收租数量',
    un_rent_collect_amount   decimal(10, 2)    not null   default 0 comment '未收租金额',
    penalty_amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '滞纳金',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);

DROP TABLE IF EXISTS r_advertising_statistics;
CREATE TABLE r_advertising_statistics(
    id              bigint unsigned   not null   auto_increment,
    statistics_date       date not null   comment '统计日期',
    advertising_num bigint unsigned NOT NULL DEFAULT '0' comment '广告位数量',
    un_rent_advertising_num bigint unsigned NOT NULL DEFAULT '0' comment '未租赁广告位数量',
    rent_advertising_num bigint unsigned NOT NULL DEFAULT '0' comment '已租赁广告位数量',
    rent_collect_num   bigint unsigned NOT NULL DEFAULT '0' comment '已收租数量',
    rent_collect_amount   decimal(10, 2)    not null   default 0 comment '已收租金额',
    un_rent_collect_num   bigint unsigned NOT NULL DEFAULT '0' comment '未已收租数量',
    un_rent_collect_amount   decimal(10, 2)    not null   default 0 comment '未收租金额',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);
-- 集中器
drop table if exists d_energy_rtu;
create table d_energy_rtu
(
    id            bigint unsigned auto_increment
        primary key,
    code          varchar(40)            not null comment '编码',
    name          varchar(20)            not null comment '名称',
    status        tinyint     default 0  not null comment '状态：0.离线，1.在线',
    on_line_time  datetime               null comment '最后在线时间',
    off_line_time datetime               null comment '离线时间',
    creator_id    bigint unsigned        not null comment '创建人id',
    creator_name  varchar(30) default '' not null comment '创建人姓名',
    create_time   datetime               not null comment '创建时间',
    modifier_id   bigint unsigned        not null comment '修改人id',
    modifier_name varchar(30) default '' not null comment '修改人姓名',
    modified_time datetime               not null comment '修改时间',
    is_disabled   tinyint(1)  default 0  not null comment '禁用（0：未禁用，1：已禁用）',
    constraint uk_energy_rtu_code
        unique (code)
);
-- 计量点
drop table if exists d_energy_metering_point;
create table d_energy_metering_point
(
    id            bigint unsigned auto_increment
        primary key,
    code          varchar(100)                 not null comment '设备编码',
    name          varchar(255)                 not null comment '设备名称',
    type          tinyint unsigned default '0' not null comment '能源类型 1 电；2 水',
    rtu_code      varchar(20)      default ''  not null comment '集中器编码',
    rtu_name      varchar(20)      default ''  not null comment '集中器名',
    rate          decimal(8, 4)                null comment '倍率',
    status        tinyint          default 0   not null comment '状态：0.离线，1.在线',
    power_status  tinyint          default -1  not null comment '状态：-1.未知 0.合闸 1.拉闸',
    creator_id    bigint unsigned              not null comment '创建人id',
    creator_name  varchar(30)      default ''  not null comment '创建人姓名',
    create_time   datetime                     not null comment '创建时间',
    modifier_id   bigint unsigned              not null comment '修改人id',
    modifier_name varchar(30)      default ''  not null comment '修改人姓名',
    modified_time datetime                     not null comment '修改时间',
    is_disabled   tinyint(1)       default 0   not null comment '禁用（0：未禁用，1：已禁用）',
    constraint uk_holley_point_code
        unique (code)
);
-- 计量点读数
drop table if exists d_energy_metering_point_readings;
create table d_energy_metering_point_readings
(
    id               bigint unsigned auto_increment
        primary key,
    rtu_code         varchar(20)                  not null comment '集中器编码',
    point_code       varchar(20)                  not null comment '设备编码',
    point_rate       decimal(8, 4)                null comment '计量点倍率',
    point_type       tinyint unsigned default '0' null comment '计量点类型 1 电；2 水',
    realty_serial_no varchar(30)                  null comment '物业编号',
    realty_name      varchar(60)                  null comment '物业名称',
    readings         decimal(11, 2)               not null comment '读数',
    data_time        datetime                     not null comment '数据时间',
    grab_time        datetime                     not null comment '抓取时间',
    constraint uk_point_code_data_time
        unique (point_code, data_time)
);
-- 计量点最新读数
drop table if exists d_energy_metering_point_readings_new;
create table d_energy_metering_point_readings_new
(
    id               bigint unsigned auto_increment
        primary key,
    rtu_code         varchar(20)                  not null comment '集中器编码',
    point_code       varchar(20)                  not null comment '设备编码',
    point_rate       decimal(8, 4)                null comment '计量点倍率',
    point_type       tinyint unsigned default '0' null comment '计量点类型 1 电；2 水',
    realty_serial_no varchar(30)                  null comment '物业编号',
    realty_name      varchar(60)                  null comment '物业名称',
    readings         decimal(11, 2)               not null comment '读数',
    data_time        datetime                     not null comment '数据时间',
    grab_time        datetime                     not null comment '抓取时间',
    constraint uk_new_point_code_data_time
        unique (point_code, data_time)
);

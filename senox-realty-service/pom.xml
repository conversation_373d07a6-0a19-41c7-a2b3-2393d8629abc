<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>senox-realty-service</artifactId>

    <parent>
        <artifactId>senox-realty</artifactId>
        <groupId>com.senox.realty</groupId>
        <version>1.3.0-SNAPSHOT</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.senox.realty</groupId>
            <artifactId>senox-realty-api</artifactId>
            <version>1.3.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.senox</groupId>
            <artifactId>common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.senox.user</groupId>
            <artifactId>senox-user-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.senox.pm</groupId>
            <artifactId>senox-payment-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.senox.dm</groupId>
            <artifactId>senox-device-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.200</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>io.foldright</groupId>
            <artifactId>cffu</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>

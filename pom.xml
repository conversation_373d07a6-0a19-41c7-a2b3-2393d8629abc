<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.senox.realty</groupId>
    <artifactId>senox-realty</artifactId>
    <version>1.3.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>senox-realty-api</module>
        <module>senox-realty-service</module>
    </modules>

    <parent>
        <artifactId>senox-base</artifactId>
        <groupId>com.senox</groupId>
        <version>1.2.6-SNAPSHOT</version>
    </parent>

    <properties>
        <senox-base.version>1.2.6-SNAPSHOT</senox-base.version>
        <senox-user.version>1.2.5-SNAPSHOT</senox-user.version>
        <senox-payment.version>1.2.1-SNAPSHOT</senox-payment.version>
        <senox-device.verion>1.2.2-SNAPSHOT</senox-device.verion>
        <cffu.verion>1.0.0-Alpha30</cffu.verion>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.senox</groupId>
                <artifactId>api</artifactId>
                <version>${senox-base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.senox</groupId>
                <artifactId>common</artifactId>
                <version>${senox-base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.senox.user</groupId>
                <artifactId>senox-user-api</artifactId>
                <version>${senox-user.version}</version>
            </dependency>

            <dependency>
                <groupId>com.senox.pm</groupId>
                <artifactId>senox-payment-api</artifactId>
                <version>${senox-payment.version}</version>
            </dependency>

            <dependency>
                <groupId>com.senox.dm</groupId>
                <artifactId>senox-device-api</artifactId>
                <version>${senox-device.verion}</version>
            </dependency>

            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>2.3.0</version>
            </dependency>

            <dependency>
                <groupId>io.foldright</groupId>
                <artifactId>cffu</artifactId>
                <version>${cffu.verion}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>

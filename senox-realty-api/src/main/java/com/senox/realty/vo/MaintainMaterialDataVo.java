package com.senox.realty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/25 9:41
 */
@ApiModel("维修物料")
@Getter
@Setter
public class MaintainMaterialDataVo implements Serializable {

    private static final long serialVersionUID = -8707201737177373822L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("派单id")
    private Long jobId;

    @ApiModelProperty("派工单号")
    private String jobNo;

    @ApiModelProperty("维修类型 1:公共土建,2:公共水电,3:公共其他,4:客户土建,5:客户水电,6:客户其他")
    private Integer maintainType;

    @ApiModelProperty("出库单号")
    private String outNo;

    @ApiModelProperty("收费状态：0未支付完；1已支付完")
    private Integer status;

    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    @ApiModelProperty("收费年份")
    private Integer chargeYear;

    @ApiModelProperty("收费月份")
    private Integer chargeMonth;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("部门")
    private Long handlerDeptId;

    @ApiModelProperty("部门名")
    private String handlerDeptName;

    @ApiModelProperty("领料人")
    private String receivePerson;

    @ApiModelProperty("物料明细")
    private List<MaintainMaterialItemVo> materialItemVos;
}

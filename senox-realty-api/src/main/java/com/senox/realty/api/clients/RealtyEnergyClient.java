package com.senox.realty.api.clients;

import com.senox.common.validation.groups.Add;
import com.senox.common.vo.BillTimeVo;
import com.senox.common.vo.PageResult;
import com.senox.common.constant.device.EnergyType;
import com.senox.realty.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.senox.realty.api.RealtyEnergyServiceUrl.*;
import static com.senox.realty.api.RealtyServiceUrl.SERVICE_NAME;

/**
 * <AUTHOR>
 * @date 2022-12-2
 */
@FeignClient(SERVICE_NAME)
public interface RealtyEnergyClient {
    /**
     * 计量设备绑定物业
     *
     * @param realtyBindPoint 绑定vo
     */
    @PostMapping(ENERGY_METERING_POINT_BIND)
    void meteringPointBindRealty(@Validated({Add.class}) @RequestBody RealtyBindEnergyMeteringPointVo realtyBindPoint);

    /**
     * 物业计量点统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    @PostMapping(ENERGY_METERING_POINT_LIST_COUNT)
    Integer meteringPointCountList(@RequestBody RealtyToEnergyMeteringPointSearchVo search);

    /**
     * 物业计量点列表
     *
     * @param search 查询参数
     */
    @PostMapping(ENERGY_METERING_POINT_LIST)
    PageResult<RealtyToEnergyMeteringPointVo> meteringPoint(@RequestBody RealtyToEnergyMeteringPointSearchVo search);

    /**
     * 物业计量点表码列表
     *
     * @param search 查询参数
     */
    @PostMapping(ENERGY_METERING_POINT_READING_LIST)
    PageResult<RealtyToEnergyMeteringPointReadingsVo> meteringPointReadingsList(@RequestBody RealtyToEnergyMeteringPointReadingsSearchVo search);

    /**
     * 同步计量点读数
     *
     * @param realtyWeBatch 参数
     */
    @PostMapping(ENERGY_METER_POINT_READINGS_SYNC)
    void syncMeteringPoint(@RequestBody RealtyWeBatchVo realtyWeBatch);

    /**
     * 自动绑定计量设备
     *
     * @param search 查询参数
     * @return 返回处理结果
     */
    @PostMapping(ENERGY_METER_POINT_AUTOMATION_BIND)
    PointBindRealtyResult automaticMeteringPointBindRealty(@RequestBody RealtyToEnergyMeteringPointSearchVo search);

    /**
     * 计量点作废
     * @param meteringPointCode 计量点编号
     */
    @GetMapping(ENERGY_METER_POINT_CANCEL)
    void meteringPointCancel(@PathVariable String meteringPointCode);

    /**
     * 添加能源消费单元
     *
     * @param unit
     * @return
     */
    @PostMapping(CONSUME_UNIT_ADD)
    Long addConsumeUnit(@RequestBody EnergyConsumeUnitVo unit);

    /**
     * 更新能源消费单元
     *
     * @param unit
     */
    @PostMapping(CONSUME_UNIT_UPDATE)
    void updateConsumeUnit(@RequestBody EnergyConsumeUnitVo unit);

    /**
     * 获取能源消费单元详情
     *
     * @param id
     * @return
     */
    @GetMapping(CONSUME_UNIT_GET)
    EnergyConsumeUnitVo findConsumeUnitById(@PathVariable Long id);

    /**
     * 能源消费单元列表
     *
     * @param type
     * @return
     */
    @PostMapping(CONSUME_UNIT_LIST)
    List<EnergyConsumeUnitVo> listConsumeUnit(@RequestParam("type") Integer type);

    /**
     * 能源损益生成
     *
     * @param billTime
     * @param type
     * @return
     */
    @PostMapping(PROFIT_GENERATE)
    EnergyProfitVo generateProfit(@RequestBody BillTimeVo billTime, @RequestParam EnergyType type);

    /**
     * 能源损益保存
     *
     * @param profitEdit
     * @return
     */
    @PostMapping(PROFIT_SAVE)
    EnergyProfitVo saveProfit(@RequestBody EnergyProfitEditVo profitEdit);

    /**
     * 能源损益获取
     *
     * @param id
     * @return
     */
    @GetMapping(PROFIT_GET)
    EnergyProfitVo findProfitById(@PathVariable Long id);

    /**
     * 能源损益页
     *
     * @param search
     * @return
     */
    @PostMapping(PROFIT_PAGE)
    PageResult<EnergyProfitVo> listProfitPage(@RequestBody EnergyProfitSearchVo search);

    /**
     * 能源损益明细
     *
     * @param profitId
     * @return
     */
    @PostMapping(PROFIT_ITEM_LIST)
    List<EnergyProfitItemVo> listProfitItem(@PathVariable Long profitId);
}
